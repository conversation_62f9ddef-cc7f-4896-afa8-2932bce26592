using Microsoft.Extensions.Logging;
using OrderFlowCore.Application.Common;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Interfaces.Repositories;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Core.Entities;

namespace OrderFlowCore.Application.Services;

public class NotificationService : INotificationService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IDirectManagerOrderService _directManagerOrderService;
    private readonly IAssistantManagerOrderService _assistantManagerOrderService;
    private readonly ISupervisorOrderService _supervisorOrderService;
    private readonly IHRCoordinatorOrderService _hrCoordinatorOrderService;
    private readonly IHRManagerService _hrManagerService;
    private readonly ILogger<NotificationService> _logger;

    public NotificationService(
        IUnitOfWork unitOfWork,
        IDirectManagerOrderService directManagerOrderService,
        IAssistantManagerOrderService assistantManagerOrderService,
        ISupervisorOrderService supervisorOrderService,
        IHRCoordinatorOrderService hrCoordinatorOrderService,
        IHRManagerService hrManagerService,
        ILogger<NotificationService> logger)
    {
        _unitOfWork = unitOfWork;
        _directManagerOrderService = directManagerOrderService;
        _assistantManagerOrderService = assistantManagerOrderService;
        _supervisorOrderService = supervisorOrderService;
        _hrCoordinatorOrderService = hrCoordinatorOrderService;
        _hrManagerService = hrManagerService;
        _logger = logger;
    }

    public async Task<ServiceResult<NotificationResponseDto>> GetUnreadNotificationsAsync(string username, UserRole userRole, string? roleType)
    {
        try
        {
            var orderNotificationResult = await GetOrderNotificationAsync(username, userRole, roleType);
            
            if (!orderNotificationResult.IsSuccess)
            {
                return ServiceResult<NotificationResponseDto>.Failure(orderNotificationResult.Message);
            }

            var orderNotification = orderNotificationResult.Data!;
            var notifications = new List<NotificationDto>();

            // Create a notification for orders if there are any
            if (orderNotification.OrderCount > 0)
            {
                notifications.Add(new NotificationDto
                {
                    Id = 1, // For now, we'll use a simple ID
                    Title = "طلبات جديدة",
                    Message = orderNotification.NotificationMessage,
                    ActionUrl = GetActionUrlForRole(userRole),
                    IsRead = false,
                    CreatedAt = DateTime.Now,
                    TimeAgo = "الآن"
                });
            }

            var response = new NotificationResponseDto
            {
                Success = true,
                Count = notifications.Count,
                Notifications = notifications
            };

            return ServiceResult<NotificationResponseDto>.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting unread notifications for user {Username}", username);
            return ServiceResult<NotificationResponseDto>.Failure($"خطأ في جلب الإشعارات: {ex.Message}");
        }
    }

    public async Task<ServiceResult<OrderNotificationDto>> GetOrderNotificationAsync(string username, UserRole userRole, string? roleType)
    {
        try
        {
            var orderCount = 0;
            var notificationMessage = "";

            switch (userRole)
            {
                case UserRole.DirectManager:
                    if (!string.IsNullOrEmpty(roleType))
                    {
                        var result = await _directManagerOrderService.GetPendingOrdersForDirectMangerAsync(roleType);
                        if (result.IsSuccess)
                        {
                            orderCount = result.Data?.Count ?? 0;
                            notificationMessage = orderCount > 0 
                                ? $"لديك {orderCount} طلب في انتظار الموافقة"
                                : "لا توجد طلبات في انتظار الموافقة";
                        }
                    }
                    break;

                case UserRole.AssistantManager:
                    if (!string.IsNullOrEmpty(roleType))
                    {
                        var assistantManagerType = AssistantManagerTypeExtensions.FromDisplayString(roleType);
                        if (assistantManagerType != AssistantManagerType.Unknown)
                        {
                            var result = await _assistantManagerOrderService.GetAssistantManagerOrdersAsync(assistantManagerType);
                            if (result.IsSuccess)
                            {
                                orderCount = result.Data?.Count ?? 0;
                                notificationMessage = orderCount > 0
                                    ? $"لديك {orderCount} طلب في انتظار المراجعة"
                                    : "لا توجد طلبات في انتظار المراجعة";
                            }
                        }
                    }
                    break;

                case UserRole.Supervisor:
                    if (!string.IsNullOrEmpty(roleType))
                    {
                        var result = await _supervisorOrderService.GetSupervisorOrdersAsync(roleType);
                        if (result.IsSuccess)
                        {
                            orderCount = result.Data?.Count ?? 0;
                            notificationMessage = orderCount > 0 
                                ? $"لديك {orderCount} طلب في انتظار التنفيذ"
                                : "لا توجد طلبات في انتظار التنفيذ";
                        }
                    }
                    break;

                case UserRole.Coordinator:
                    var coordinatorResult = await _hrCoordinatorOrderService.GetHRCoordinatorOrdersAsync();
                    if (coordinatorResult.IsSuccess)
                    {
                        orderCount = coordinatorResult.Data?.Count ?? 0;
                        notificationMessage = orderCount > 0 
                            ? $"لديك {orderCount} طلب في انتظار التنسيق"
                            : "لا توجد طلبات في انتظار التنسيق";
                    }
                    break;

                case UserRole.Manager:
                    var managerResult = await _hrManagerService.GetHRManagerOrdersAsync();
                    if (managerResult.IsSuccess)
                    {
                        orderCount = managerResult.Data?.Count ?? 0;
                        notificationMessage = orderCount > 0 
                            ? $"لديك {orderCount} طلب في انتظار الإدارة"
                            : "لا توجد طلبات في انتظار الإدارة";
                    }
                    break;

                default:
                    notificationMessage = "لا توجد طلبات";
                    break;
            }

            var notification = new OrderNotificationDto
            {
                OrderCount = orderCount,
                UserRole = userRole.ToString(),
                RoleType = roleType,
                NotificationMessage = notificationMessage
            };

            return ServiceResult<OrderNotificationDto>.Success(notification);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting order notification for user {Username}", username);
            return ServiceResult<OrderNotificationDto>.Failure($"خطأ في جلب إشعارات الطلبات: {ex.Message}");
        }
    }

    public async Task<ServiceResult> MarkAsReadAsync(int notificationId)
    {
        // For now, we'll just return success since we're not storing notifications in database
        // This can be implemented later when we add a notifications table
        await Task.CompletedTask;
        return ServiceResult.Success("تم تحديد الإشعار كمقروء");
    }

    public async Task<ServiceResult> MarkAllAsReadAsync(string username)
    {
        // For now, we'll just return success since we're not storing notifications in database
        // This can be implemented later when we add a notifications table
        await Task.CompletedTask;
        return ServiceResult.Success("تم تحديد جميع الإشعارات كمقروءة");
    }

    private string GetActionUrlForRole(UserRole userRole)
    {
        return userRole switch
        {
            UserRole.DirectManager => "/DirectManager",
            UserRole.AssistantManager => "/AssistantManager",
            UserRole.Supervisor => "/SupervisorOrders",
            UserRole.Coordinator => "/HRCoordinator",
            UserRole.Manager => "/HRManager",
            _ => "/Dashboard"
        };
    }
}
