using DocumentFormat.OpenXml.Drawing.Charts;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using OrderFlowCore.Application.Common;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Helper;
using OrderFlowCore.Application.Interfaces.Repositories;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Core.Entities;
using OrderFlowCore.Core.Exceptions;
using OrderFlowCore.Core.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace OrderFlowCore.Application.Services
{
    
    public class OrderManagementService : IOrderManagementService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly OrderServiceOptions _options;
        private readonly IFileService _fileService;
        private readonly ILogger<OrderManagementService> _logger;

        public OrderManagementService(
            IUnitOfWork unitOfWork,
            IOptions<OrderServiceOptions> options,
            IFileService fileService,
            ILogger<OrderManagementService> logger)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
            _fileService = fileService ?? throw new ArgumentNullException(nameof(fileService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task<ServiceResult<DropdownDataDto>> GetDropdownDataAsync()
        {
            try
            {
                var dropdownData = new DropdownDataDto();

                // Load data from repositories with error handling
                var departments = await _unitOfWork.Departments.GetAllAsync();
                var orderTypes = await _unitOfWork.OrdersTypes.GetAllAsync();
                var jobTypes = await _unitOfWork.JobTypes.GetAllAsync();
                var nationalities = await _unitOfWork.Nationalities.GetAllAsync();
                var employmentTypes = await _unitOfWork.EmploymentTypes.GetAllAsync();
                var qualifications = await _unitOfWork.Qualifications.GetAllAsync();

                // Convert data to DropdownItemDto
                dropdownData.Departments = departments.Select(d => new DropdownItemDto { Value = d.Name, Text = d.Name }).ToList();
                dropdownData.OrderTypes = orderTypes.Select(ot => new DropdownItemDto { Value = ot.Name, Text = ot.Name }).ToList();
                dropdownData.JobTitles = jobTypes.Select(jt => new DropdownItemDto { Value = jt.Name, Text = jt.Name }).ToList();
                dropdownData.Nationalities = nationalities.Select(n => new DropdownItemDto { Value = n.Name, Text = n.Name }).ToList();
                dropdownData.EmploymentTypes = employmentTypes.Select(et => new DropdownItemDto { Value = et.Name, Text = et.Name }).ToList();
                dropdownData.Qualifications = qualifications.Select(q => new DropdownItemDto { Value = q.Name, Text = q.Name }).ToList();

                return ServiceResult<DropdownDataDto>.Success(dropdownData);
            }
            catch (Exception ex)
            {
                return ServiceResult<DropdownDataDto>.Failure($"خطأ في تحميل بيانات القوائم المنسدلة: {ex.Message}");
            }
        }

        public async Task<ServiceResult<OrderSummaryDto>> CreateOrderAsync(OrderNewDto orderDto)
        {
            try
            {
                // Validate input
                var validationResult = ValidateOrderDto(orderDto);
                if (!validationResult.IsSuccess)
                {
                    return ServiceResult<OrderSummaryDto>.Failure(validationResult.Message, validationResult.Errors);
                }

                // Create order entity with trimmed values
                var order = CreateOrderEntity(orderDto);

                // Check if employee exists (optimization: single query)
                var employee = await _unitOfWork.Employees.GetByCivilNumberAsync(order.CivilRecord);

                // Begin transaction for data consistency
                await _unitOfWork.BeginTransactionAsync();
                try
                {
                    // Create employee if not exists
                    if (employee == null)
                    {
                        var employeeDto = CreateEmployeeDto(order);
                        await _unitOfWork.Employees.CreateAsync(employeeDto);
                    }

                    // Add order to context
                    await _unitOfWork.Orders.AddAsync(order);
                    await _unitOfWork.SaveChangesAsync();

                    // Handle file uploads after order is saved (so we have order.Id)
                    if (orderDto.Attachments?.Any() == true)
                    {
                        var attachmentResult = await HandleFileUploadsAsync(orderDto.Attachments, order);
                        if (!attachmentResult.IsSuccess)
                        {
                            await _unitOfWork.RollbackTransactionAsync();
                            return ServiceResult<OrderSummaryDto>.Failure(attachmentResult.Message);
                        }

                        // Update order with file URLs
                        UpdateOrderFileUrls(order, attachmentResult.Data);
                        await _unitOfWork.SaveChangesAsync();
                    }

                    await _unitOfWork.CommitTransactionAsync();

                    return ServiceResult<OrderSummaryDto>.Success(
                        CreateOrderSummaryDto(order),
                        "تم إنشاء الطلب بنجاح");
                }
                catch
                {
                    await _unitOfWork.RollbackTransactionAsync();
                    throw;
                }
            }
            catch (Exception ex)
            {
                return ServiceResult<OrderSummaryDto>.Failure($"خطأ في إنشاء الطلب: {ex.Message}");
            }
        }

        public async Task<ServiceResult<OrderDetailsDto>> GetOrderDetailsAsync(int orderId)
        {
            try
            {
                _logger.LogInformation("Getting order details for order ID: {OrderId}", orderId);

                if (orderId <= 0)
                {
                    _logger.LogWarning("Invalid order ID provided: {OrderId}", orderId);
                    throw new ValidationException("رقم الطلب غير صحيح");
                }

                var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
                if (order == null)
                {
                    _logger.LogWarning("Order not found: {OrderId}", orderId);
                    throw new OrderNotFoundException(orderId);
                }

                var orderDetails = OrderDetailsDto.FromDomain(order);

                _logger.LogInformation("Successfully retrieved order details for order ID: {OrderId}", orderId);
                return ServiceResult<OrderDetailsDto>.Success(orderDetails);
            }
            catch (BusinessLogicException)
            {
                throw; // Re-throw business logic exceptions
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting order details for order ID: {OrderId}", orderId);
                return ServiceResult<OrderDetailsDto>.Failure($"خطأ في جلب تفاصيل الطلب: {ex.Message}");
            }
        }

        public async Task<ServiceResult<string>> GetOrderSpecialActionAsync(int orderId, string? supervisorId)
        {
            try
            {
                _logger.LogInformation("Getting order details for order ID: {OrderId}", orderId);

                if (orderId <= 0)
                {
                    _logger.LogWarning("Invalid order ID provided: {OrderId}", orderId);
                    throw new ValidationException("رقم الطلب غير صحيح");
                }

                var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
                if (order == null)
                {
                    _logger.LogWarning("Order not found: {OrderId}", orderId);
                    throw new OrderNotFoundException(orderId);
                }

                var specialAction = await _unitOfWork.SupervisorsFollowUps.GetAsync(supervisorId,order.CivilRecord);
                if (specialAction == null)
                {
                    return ServiceResult<string>.Failure("لا توجد ملاحظات خاصة من المشرف");
                }

                _logger.LogInformation("Successfully retrieved order special action for order ID: {OrderId}", orderId);
                return ServiceResult<string>.Success(specialAction.SpecialProcedure);
            }
            catch (BusinessLogicException)
            {
                throw; // Re-throw business logic exceptions
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting order details for order ID: {OrderId}", orderId);
                return ServiceResult<string>.Failure($"خطأ في جلب تفاصيل الطلب: {ex.Message}");
            }
        }
        public async Task<ServiceResult<OrderDetailsDto>> GetOrderDetailsByIdAndCivilRecordAsync(int orderId, string civilRecord)
        {
            try
            {
                if (orderId <= 0 || string.IsNullOrWhiteSpace(civilRecord))
                {
                    return ServiceResult<OrderDetailsDto>.Failure("رقم الطلب أو السجل المدني غير صحيح");
                }

                var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
                if (order == null || !order.CivilRecord.Equals(civilRecord.Trim(), StringComparison.OrdinalIgnoreCase))
                {
                    return ServiceResult<OrderDetailsDto>.Failure("رقم الطلب أو السجل المدني غير صحيح");
                }

                var orderDetails = OrderDetailsDto.FromDomain(order);
                return ServiceResult<OrderDetailsDto>.Success(orderDetails);
            }
            catch (Exception ex)
            {
                return ServiceResult<OrderDetailsDto>.Failure($"خطأ في جلب تفاصيل الطلب: {ex.Message}");
            }
        }

        public async Task<ServiceResult> UploadAttachmentAsync(int orderId, byte[] fileData, int fileNumber)
        {
            try
            {
                if (orderId <= 0)
                {
                    return ServiceResult.Failure("رقم الطلب غير صحيح");
                }

                if (fileData == null || fileData.Length == 0)
                {
                    return ServiceResult.Failure("بيانات الملف فارغة");
                }


                var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
                if (order == null)
                {
                    return ServiceResult.Failure("لم يتم العثور على الطلب");
                }

                // Determine which file slot to update and delete old file if exists
                string oldFileUrl = null;
                switch (fileNumber)
                {
                    case 1:
                        oldFileUrl = order.File1Url;
                        break;
                    case 2:
                        oldFileUrl = order.File2Url;
                        break;
                    case 3:
                        oldFileUrl = order.File3Url;
                        break;
                    case 4:
                        oldFileUrl = order.File4Url;
                        break;
                    default:
                        return ServiceResult.Failure("رقم المرفق غير صحيح");
                }

                if (!string.IsNullOrEmpty(oldFileUrl))
                {
                    await _fileService.DeleteFileAsync(oldFileUrl);
                }

                // Upload new file
                var uploadResult = await _fileService.UploadFileAsync(fileData, fileNumber.ToString(), $"order_{order.Id}_{order.CivilRecord}");
                if (!uploadResult.IsSuccess)
                {
                    return ServiceResult.Failure(uploadResult.Message);
                }
                var fileUrl = uploadResult.Data;

                // Update the correct file URL property
                switch (fileNumber)
                {
                    case 1:
                        order.File1Url = fileUrl;
                        break;
                    case 2:
                        order.File2Url = fileUrl;
                        break;
                    case 3:
                        order.File3Url = fileUrl;
                        break;
                    case 4:
                        order.File4Url = fileUrl;
                        break;
                }

                await _unitOfWork.Orders.UpdateAsync(order);
                await _unitOfWork.SaveChangesAsync();

                return ServiceResult.Success("تم رفع المرفق بنجاح");
            }
            catch (Exception ex)
            {
                return ServiceResult.Failure($"خطأ في رفع المرفق: {ex.Message}");
            }
        }

        public async Task<ServiceResult<byte[]>> DownloadAttachmentsZipAsync(int orderId)
        {
            try
            {
                if (orderId <= 0)
                {
                    return ServiceResult<byte[]>.Failure("رقم الطلب غير صحيح");
                }

                var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
                if (order == null)
                {
                    return ServiceResult<byte[]>.Failure("لم يتم العثور على الطلب");
                }

                var fileUrls = new List<string>();
                if (!string.IsNullOrEmpty(order.File1Url)) fileUrls.Add(order.File1Url);
                if (!string.IsNullOrEmpty(order.File2Url)) fileUrls.Add(order.File2Url);
                if (!string.IsNullOrEmpty(order.File3Url)) fileUrls.Add(order.File3Url);
                if (!string.IsNullOrEmpty(order.File4Url)) fileUrls.Add(order.File4Url);

                if (fileUrls.Count == 0)
                {
                    return ServiceResult<byte[]>.Failure("لا توجد مرفقات");
                }

                // Use FileService for zipping
                var zipResult = await _fileService.DownloadFilesZipAsync(fileUrls);
                if (!zipResult.IsSuccess)
                {
                    return ServiceResult<byte[]>.Failure(zipResult.Message);
                }

                return ServiceResult<byte[]>.Success(zipResult.Data);
            }
            catch (Exception ex)
            {
                return ServiceResult<byte[]>.Failure($"خطأ في تحميل المرفقات: {ex.Message}");
            }
        }

        #region Private Helper Methods

        private ServiceResult ValidateOrderDto(OrderNewDto orderDto)
        {
            var errors = new List<string>();

            if (orderDto == null)
            {
                return ServiceResult.Failure("بيانات الطلب مطلوبة");
            }

            // Validate required fields
            if (string.IsNullOrWhiteSpace(orderDto.EmployeeName))
                errors.Add("اسم الموظف مطلوب");

            if (string.IsNullOrWhiteSpace(orderDto.JobTitle))
                errors.Add("الوظيفة مطلوبة");

            if (string.IsNullOrWhiteSpace(orderDto.EmployeeNumber))
                errors.Add("رقم الموظف مطلوب");

            if (string.IsNullOrWhiteSpace(orderDto.CivilRecord))
                errors.Add("السجل المدني مطلوب");

            if (string.IsNullOrWhiteSpace(orderDto.Nationality))
                errors.Add("الجنسية مطلوبة");

            if (string.IsNullOrWhiteSpace(orderDto.MobileNumber))
                errors.Add("رقم الجوال مطلوب");

            if (string.IsNullOrWhiteSpace(orderDto.Department))
                errors.Add("القسم مطلوب");

            if (string.IsNullOrWhiteSpace(orderDto.EmploymentType))
                errors.Add("نوع التوظيف مطلوب");

            if (string.IsNullOrWhiteSpace(orderDto.Qualification))
                errors.Add("المؤهل مطلوب");

            if (string.IsNullOrWhiteSpace(orderDto.OrderType))
                errors.Add("نوع الطلب مطلوب");

            // Validate mobile number format
            if (!string.IsNullOrWhiteSpace(orderDto.MobileNumber) &&
                !Regex.IsMatch(orderDto.MobileNumber, @"^[0-9+\-\s()]+$"))
            {
                errors.Add("صيغة رقم الجوال غير صحيحة");
            }

            // Validate attachments count
            if (orderDto.Attachments?.Count > _options.MaxAttachments)
            {
                errors.Add($"الحد الأقصى للمرفقات هو {_options.MaxAttachments}");
            }

            return errors.Any()
                ? ServiceResult.Failure("فشل في التحقق من صحة البيانات", errors)
                : ServiceResult.Success();
        }

        // Extract order creation logic
        private static OrdersTable CreateOrderEntity(OrderNewDto orderDto)
        {
            return new OrdersTable
            {
                EmployeeName = orderDto.EmployeeName?.Trim() ?? string.Empty,
                JobTitle = orderDto.JobTitle?.Trim() ?? string.Empty,
                EmployeeNumber = orderDto.EmployeeNumber?.Trim() ?? string.Empty,
                CivilRecord = orderDto.CivilRecord?.Trim() ?? string.Empty,
                Nationality = orderDto.Nationality?.Trim() ?? string.Empty,
                MobileNumber = orderDto.MobileNumber?.Trim() ?? string.Empty,
                Department = orderDto.Department?.Trim() ?? string.Empty,
                EmploymentType = orderDto.EmploymentType?.Trim() ?? string.Empty,
                Qualification = orderDto.Qualification?.Trim() ?? string.Empty,
                OrderType = orderDto.OrderType?.Trim() ?? string.Empty,
                Details = orderDto.Details?.Trim() ?? string.Empty,
                CreatedAt = DateTime.UtcNow,
                OrderStatus = OrderStatus.DM,
                ConfirmedByDepartmentManager = OrderHelper.AssignedToDirectManager(orderDto.Department),
            };
        }

        // Extract employee creation logic
        private static EmployeeDto CreateEmployeeDto(OrdersTable order)
        {
            return new EmployeeDto
            {
                CivilNumber = order.CivilRecord,
                Name = order.EmployeeName,
                Job = order.JobTitle,
                Nationality = order.Nationality,
                Mobile = order.MobileNumber,
                EmploymentType = order.EmploymentType,
                Qualification = order.Qualification,
                EmployeeNumber = order.EmployeeNumber,
                Department = order.Department
            };
        }

        // Extract file upload handling
        private async Task<ServiceResult<List<string>>> HandleFileUploadsAsync(
            List<byte[]> attachments,
            OrdersTable order)
        {
            var folderName = $"order_{order.Id}_{order.CivilRecord}";
            return await _fileService.UploadFilesAsync(attachments, folderName);
        }

        // Extract file URL assignment logic
        private void UpdateOrderFileUrls(OrdersTable order, List<string> fileUrls)
        {
            var maxFiles = Math.Min(fileUrls.Count, _options.MaxAttachments);

            for (int i = 0; i < maxFiles; i++)
            {
                switch (i)
                {
                    case 0: order.File1Url = fileUrls[i]; break;
                    case 1: order.File2Url = fileUrls[i]; break;
                    case 2: order.File3Url = fileUrls[i]; break;
                    case 3: order.File4Url = fileUrls[i]; break;
                }
            }
        }

        // Extract DTO creation
        private static OrderSummaryDto CreateOrderSummaryDto(OrdersTable order)
        {
            return new OrderSummaryDto
            {
                Id = order.Id,
                EmployeeName = order.EmployeeName,
                OrderType = order.OrderType,
                Department = order.Department,
                CreatedAt = order.CreatedAt,
                OrderStatus = order.OrderStatus
            };
        }
        #endregion
    }
}
