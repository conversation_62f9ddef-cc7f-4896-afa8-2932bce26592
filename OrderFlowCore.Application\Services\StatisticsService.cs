using Microsoft.Extensions.Logging;
using OrderFlowCore.Application.Common;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Helper;
using OrderFlowCore.Application.Interfaces.Repositories;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Core.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace OrderFlowCore.Application.Services
{
    public class StatisticsService : IStatisticsService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<StatisticsService> _logger;

        // Define supervisor mappings as static readonly for better performance
        private static readonly Dictionary<string, string> SupervisorMappings = new()
        {
            ["SupervisorOfEmployeeServices"] = "خدمات الموظفين",
            ["SupervisorOfHumanResourcesPlanning"] = "إدارة تخطيط الموارد البشرية",
            ["SupervisorOfInformationTechnology"] = "إدارة تقنية المعلومات",
            ["SupervisorOfAttendance"] = "مراقبة الدوام",
            ["SupervisorOfMedicalRecords"] = "السجلات الطبية",
            ["SupervisorOfPayrollAndBenefits"] = "إدارة الرواتب والاستحقاقات",
            ["SupervisorOfLegalAndCompliance"] = "إدارة القانونية والالتزام",
            ["SupervisorOfHumanResourcesServices"] = "خدمات الموارد البشرية",
            ["SupervisorOfHousing"] = "إدارة الإسكان",
            ["SupervisorOfFiles"] = "قسم الملفات",
            ["SupervisorOfOutpatientClinics"] = "العيادات الخارجية",
            ["SupervisorOfSocialSecurity"] = "التأمينات الاجتماعية",
            ["SupervisorOfInventoryControl"] = "وحدة مراقبة المخزون",
            ["SupervisorOfRevenueDevelopment"] = "إدارة تنمية الإيرادات",
            ["SupervisorOfSecurity"] = "إدارة الأمن و السلامة",
            ["SupervisorOfMedicalConsultation"] = "الطب الاتصالي"
        };

        // Cache order status sets for better performance
        private static readonly HashSet<OrderStatus> PendingStatuses = new()
        {
            OrderStatus.DM, OrderStatus.A1, OrderStatus.A2, OrderStatus.A3,
            OrderStatus.A4, OrderStatus.B, OrderStatus.C, OrderStatus.D
        };

        private static readonly HashSet<OrderStatus> CancelledStatuses = new()
        {
            OrderStatus.CancelledByDepartmentManager,
            OrderStatus.CancelledByAssistantManager,
            OrderStatus.CancelledByCoordinator,
            OrderStatus.CancelledByManager
        };

        public StatisticsService(
            IUnitOfWork unitOfWork,
            ILogger<StatisticsService> logger)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task<ServiceResult<GeneralStatisticsDto>> GetGeneralStatisticsAsync()
        {
            try
            {
                // Load all orders once and work with in-memory collection
                var allOrders = (await _unitOfWork.Orders.GetAllAsync()).ToList();

                var totalRequests = allOrders.Count;
                var completedRequests = allOrders.Count(o => o.OrderStatus == OrderStatus.Accepted);
                var pendingRequests = allOrders.Count(o => PendingStatuses.Contains(o.OrderStatus));

                // Calculate average completion time more efficiently
                var avgTimeToComplete = CalculateAverageCompletionTime(
                    allOrders.Where(o => o.OrderStatus == OrderStatus.Accepted &&
                                   !string.IsNullOrEmpty(o.HumanResourcesManager)));

                var statistics = new GeneralStatisticsDto
                {
                    TotalRequests = totalRequests,
                    CompletedRequests = completedRequests,
                    PendingRequests = pendingRequests,
                    AverageTimeToComplete = avgTimeToComplete
                };

                return ServiceResult<GeneralStatisticsDto>.Success(statistics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting general statistics");
                return ServiceResult<GeneralStatisticsDto>.Failure($"حدث خطأ أثناء جلب الإحصائيات العامة: {ex.Message}");
            }
        }

        public async Task<ServiceResult<StageStatisticsDto>> GetStageStatisticsAsync()
        {
            try
            {
                // Load all orders once and work with in-memory collection
                var allOrders = (await _unitOfWork.Orders.GetAllAsync()).ToList();

                // Use LINQ GroupBy for more efficient counting
                var statusCounts = allOrders
                    .GroupBy(o => o.OrderStatus)
                    .ToDictionary(g => g.Key, g => g.Count());

                var stageStats = new StageStatisticsDto
                {
                    DepartmentManager = statusCounts.GetValueOrDefault(OrderStatus.DM, 0),
                    AssistantManagerA1 = statusCounts.GetValueOrDefault(OrderStatus.A1, 0),
                    AssistantManagerA2 = statusCounts.GetValueOrDefault(OrderStatus.A2, 0),
                    AssistantManagerA3 = statusCounts.GetValueOrDefault(OrderStatus.A3, 0),
                    AssistantManagerA4 = statusCounts.GetValueOrDefault(OrderStatus.A4, 0),
                    HRCoordinator = statusCounts.GetValueOrDefault(OrderStatus.B, 0),
                    Supervisors = statusCounts.GetValueOrDefault(OrderStatus.C, 0),
                    HRManager = statusCounts.GetValueOrDefault(OrderStatus.D, 0)
                };

                return ServiceResult<StageStatisticsDto>.Success(stageStats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting stage statistics");
                return ServiceResult<StageStatisticsDto>.Failure($"حدث خطأ أثناء جلب إحصائيات المراحل: {ex.Message}");
            }
        }

        public async Task<ServiceResult<List<OrderDepartmentStatisticsDto>>> GetDepartmentStatisticsAsync()
        {
            try
            {
                // Load all orders once and work with in-memory collection
                var allOrders = (await _unitOfWork.Orders.GetAllAsync()).ToList();

                var departmentStats = allOrders
                    .GroupBy(o => o.Department ?? "غير محدد")
                    .Select(g => new OrderDepartmentStatisticsDto
                    {
                        DepartmentName = g.Key,
                        TotalOrders = g.Count(),
                        CompletedOrders = g.Count(o => o.OrderStatus == OrderStatus.Accepted),
                        PendingOrders = g.Count(o => PendingStatuses.Contains(o.OrderStatus)),
                        CancelledOrders = g.Count(o => CancelledStatuses.Contains(o.OrderStatus))
                    })
                    .OrderByDescending(d => d.TotalOrders)
                    .ToList();

                return ServiceResult<List<OrderDepartmentStatisticsDto>>.Success(departmentStats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting department statistics");
                return ServiceResult<List<OrderDepartmentStatisticsDto>>.Failure($"حدث خطأ أثناء جلب إحصائيات الأقسام: {ex.Message}");
            }
        }

        public async Task<ServiceResult<List<SupervisorStatisticsDto>>> GetSupervisorStatisticsAsync()
        {
            try
            {
                // Load all orders once and work with in-memory collection to avoid DbContext concurrency issues
                var allOrders = (await _unitOfWork.Orders.GetAllAsync()).ToList();
                var supervisorStats = new List<SupervisorStatisticsDto>();

                // Process supervisors sequentially to avoid concurrency issues
                foreach (var kvp in SupervisorMappings)
                {
                    var fieldName = kvp.Key;
                    var supervisorName = kvp.Value;

                    var ordersWithSupervisor = allOrders
                        .Where(o => !string.IsNullOrEmpty(GetSupervisorValue(o, fieldName)))
                        .ToList();

                    if (!ordersWithSupervisor.Any())
                        continue;

                    var supervisorValues = ordersWithSupervisor
                        .Select(o => GetSupervisorValue(o, fieldName))
                        .ToList();

                    var underExecution = CountByKeyword(supervisorValues, "الطلب قيد التنفيذ");
                    var completed = CountByKeyword(supervisorValues, "اعتماد");
                    var needsAction = CountByKeyword(supervisorValues, "طلب إجراء");
                    var returned = CountByKeyword(supervisorValues, "تمت الإعادة");

                    // Calculate average completion time more efficiently
                    var avgCompletionTime = CalculateSupervisorAverageTime(ordersWithSupervisor, fieldName);

                    supervisorStats.Add(new SupervisorStatisticsDto
                    {
                        SupervisorName = supervisorName,
                        UnderExecution = underExecution,
                        Completed = completed,
                        NeedsAction = needsAction,
                        Returned = returned,
                        AverageCompletionTime = avgCompletionTime
                    });
                }

                return ServiceResult<List<SupervisorStatisticsDto>>.Success(supervisorStats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting supervisor statistics");
                return ServiceResult<List<SupervisorStatisticsDto>>.Failure($"حدث خطأ أثناء جلب إحصائيات المشرفين: {ex.Message}");
            }
        }

        public async Task<ServiceResult<List<TransferTypeStatisticsDto>>> GetTransferTypeStatisticsAsync()
        {
            try
            {
                // Load all orders once and work with in-memory collection
                var allOrders = (await _unitOfWork.Orders.GetAllAsync()).ToList();

                var transferTypeStats = allOrders
                    .Where(o => !string.IsNullOrEmpty(o.TransferType))
                    .GroupBy(o => o.TransferType)
                    .Select(g => new TransferTypeStatisticsDto
                    {
                        TransferType = g.Key,
                        Count = g.Count()
                    })
                    .OrderByDescending(t => t.Count)
                    .ToList();

                return ServiceResult<List<TransferTypeStatisticsDto>>.Success(transferTypeStats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting transfer type statistics");
                return ServiceResult<List<TransferTypeStatisticsDto>>.Failure($"حدث خطأ أثناء جلب إحصائيات نوع التحويل: {ex.Message}");
            }
        }

        // Helper methods for better code organization and reusability
        private static double CalculateAverageCompletionTime(IEnumerable<Core.Models.OrdersTable> orders)
        {
            var validDays = orders
                .Select(order =>
                {
                    try
                    {
                        var outDate = OrderHelper.ExtractOutDate(order.HumanResourcesManager);
                        if (string.IsNullOrEmpty(outDate) || !DateTime.TryParse(outDate, out var completionDate))
                            return (double?)null;

                        var days = (completionDate - order.CreatedAt).TotalDays;
                        return days >= 0 ? days : (double?)null;
                    }
                    catch
                    {
                        return (double?)null;
                    }
                })
                .Where(days => days.HasValue)
                .Select(days => days.Value)
                .ToList();

            return validDays.Any() ? Math.Round(validDays.Average(), 1) : 0;
        }

        private double CalculateSupervisorAverageTime(List<Core.Models.OrdersTable> orders, string fieldName)
        {
            try
            {
                var completedOrders = orders
                    .Where(o => GetSupervisorValue(o, fieldName).Contains("اعتماد"))
                    .ToList();

                if (!completedOrders.Any())
                    return 0;

                var validDays = completedOrders
                    .Select(order =>
                    {
                        try
                        {
                            var supervisorValue = GetSupervisorValue(order, fieldName);
                            var outDate = OrderHelper.ExtractOutDate(supervisorValue);

                            if (string.IsNullOrEmpty(outDate) || !DateTime.TryParse(outDate, out var completionDate))
                                return (double?)null;

                            var inDate = OrderHelper.ExtractInDate(supervisorValue);
                            var creationDate = DateTime.TryParse(inDate, out var parsedInDate) &&
                                             parsedInDate.Date != completionDate.Date
                                ? parsedInDate
                                : order.CreatedAt;

                            var days = (completionDate - creationDate).TotalDays;
                            return days >= 0 ? days : (double?)null;
                        }
                        catch
                        {
                            return (double?)null;
                        }
                    })
                    .Where(days => days.HasValue)
                    .Select(days => days.Value)
                    .ToList();

                return validDays.Any() ? Math.Round(validDays.Average(), 1) : 0;
            }
            catch
            {
                return 0;
            }
        }

        private static int CountByKeyword(IEnumerable<string> values, string keyword)
        {
            return values.Count(value => !string.IsNullOrEmpty(value) && value.Contains(keyword));
        }

        private static string GetSupervisorValue(Core.Models.OrdersTable order, string fieldName)
        {
            try
            {
                return fieldName switch
                {
                    "SupervisorOfEmployeeServices" => order.SupervisorOfEmployeeServices ?? string.Empty,
                    "SupervisorOfHumanResourcesPlanning" => order.SupervisorOfHumanResourcesPlanning ?? string.Empty,
                    "SupervisorOfInformationTechnology" => order.SupervisorOfInformationTechnology ?? string.Empty,
                    "SupervisorOfAttendance" => order.SupervisorOfAttendance ?? string.Empty,
                    "SupervisorOfMedicalRecords" => order.SupervisorOfMedicalRecords ?? string.Empty,
                    "SupervisorOfPayrollAndBenefits" => order.SupervisorOfPayrollAndBenefits ?? string.Empty,
                    "SupervisorOfLegalAndCompliance" => order.SupervisorOfLegalAndCompliance ?? string.Empty,
                    "SupervisorOfHumanResourcesServices" => order.SupervisorOfHumanResourcesServices ?? string.Empty,
                    "SupervisorOfHousing" => order.SupervisorOfHousing ?? string.Empty,
                    "SupervisorOfFiles" => order.SupervisorOfFiles ?? string.Empty,
                    "SupervisorOfOutpatientClinics" => order.SupervisorOfOutpatientClinics ?? string.Empty,
                    "SupervisorOfSocialSecurity" => order.SupervisorOfSocialSecurity ?? string.Empty,
                    "SupervisorOfInventoryControl" => order.SupervisorOfInventoryControl ?? string.Empty,
                    "SupervisorOfRevenueDevelopment" => order.SupervisorOfRevenueDevelopment ?? string.Empty,
                    "SupervisorOfSecurity" => order.SupervisorOfSecurity ?? string.Empty,
                    "SupervisorOfMedicalConsultation" => order.SupervisorOfMedicalConsultation ?? string.Empty,
                    _ => string.Empty
                };
            }
            catch
            {
                return string.Empty;
            }
        }
    }
}