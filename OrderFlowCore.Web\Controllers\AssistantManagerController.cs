using Microsoft.AspNetCore.Mvc;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Web.ViewModels;
using Microsoft.AspNetCore.Mvc.Rendering;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;
using Microsoft.Extensions.Logging;
using System.IO.Compression;
using System.IO;
using System.Security.Claims;
using Microsoft.AspNetCore.Authorization;
using OrderFlowCore.Core.Entities;
using OrderFlowCore.Web.Extentions;
using OrderFlowCore.Web.Attributes;

namespace OrderFlowCore.Web.Controllers
{
    [AuthorizeRole(UserRole.AssistantManager)]
    public class AssistantManagerController : Controller
    {
        private readonly IAssistantManagerOrderService _assistantManagerOrderService;
        private readonly IOrderManagementService _orderManagementService;
        private readonly ILogger<AssistantManagerController> _logger;

        public AssistantManagerController(IAssistantManagerOrderService assistantManagerOrderService, IOrderManagementService orderManagementService, ILogger<AssistantManagerController> logger)
        {
            _assistantManagerOrderService = assistantManagerOrderService;
            _orderManagementService = orderManagementService;
            _logger = logger;
        }

        [HttpGet]
        public async Task<IActionResult> Index()
        {
            var userRoleType = User.GetUserRoleType();
            var assistantManagerId = AssistantManagerTypeExtensions.FromDisplayString(userRoleType);
            if (assistantManagerId == AssistantManagerType.Unknown)
            {
                TempData["ErrorMessage"] = "لم يتم تحديد مساعد المدير. يرجى تسجيل الدخول.";
                return RedirectToAction("AccessDenied", "Auth");
            }

            var viewModel = new AssistantManagerViewModel();

            // Get orders for assistant manager
            var ordersResult = await _assistantManagerOrderService.GetAssistantManagerOrdersAsync(assistantManagerId);
            if (ordersResult.IsSuccess)
            {
                viewModel.OrderNumbers = ordersResult.Data.Select(o => new SelectListItem
                {
                    Value = o.Id.ToString(),
                    Text = $"{o.Id} - {o.EmployeeName} - {o.Department}"
                }).ToList();

            }
            else
            {
                TempData["ErrorMessage"] = ordersResult.Message;
            }

            return View(viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> GetOrderDetails(int orderId)
        {
            var result = await _orderManagementService.GetOrderDetailsAsync(orderId);
            if (!result.IsSuccess)
            {
                return Json(new { success = false, message = result.Message });
            }

            return Json(new { success = true, data = result.Data });
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ConfirmOrder(int orderId)
        {

            if (orderId <= 0)
            {
                TempData["ErrorMessage"] = "يرجى اختيار رقم الطلب.";
                return RedirectToAction(nameof(Index));
            }
            var userName = User.Identity?.Name;
            var result = await _assistantManagerOrderService.ConfirmOrderByAssistantManagerAsync(orderId, userName);

            if (result.IsSuccess)
            {
                TempData["SuccessMessage"] = result.Message;
            }
            else
            {
                TempData["ErrorMessage"] = result.Message;
            }

            return RedirectToAction(nameof(Index));

        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ReturnToManager(int orderId, string returnReason)
        {

            if (orderId <= 0)
            {
                TempData["ErrorMessage"] = "يرجى اختيار رقم الطلب.";
                return RedirectToAction(nameof(Index));
            }

            if (string.IsNullOrWhiteSpace(returnReason))
            {
                TempData["ErrorMessage"] = "يرجى إدخال سبب الإعادة إلى مدير القسم.";
                return RedirectToAction(nameof(Index));
            }
            var userName = User.Identity?.Name;
            var result = await _assistantManagerOrderService.ReturnOrderToDirectManagerAsync(orderId, returnReason, userName);
            if (result.IsSuccess)
            {
                TempData["SuccessMessage"] = result.Message;
            }
            else
            {
                TempData["ErrorMessage"] = result.Message;
            }

            return RedirectToAction(nameof(Index));

        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> RejectOrder(int orderId, string rejectReason)
        {

            if (orderId <= 0)
            {
                TempData["ErrorMessage"] = "يرجى اختيار رقم الطلب.";
                return RedirectToAction(nameof(Index));
            }

            if (string.IsNullOrWhiteSpace(rejectReason))
            {
                TempData["ErrorMessage"] = "يرجى إدخال سبب الإلغاء.";
                return RedirectToAction(nameof(Index));
            }

            var userName = User.Identity?.Name;
            var result = await _assistantManagerOrderService.RejectOrderByAssistantManagerAsync(orderId, rejectReason, userName);

            if (result.IsSuccess)
            {
                TempData["SuccessMessage"] = result.Message;
            }
            else
            {
                TempData["ErrorMessage"] = result.Message;
            }

            return RedirectToAction(nameof(Index));

        }

        [HttpGet]
        public async Task<IActionResult> DownloadAttachments(int id)
        {
            var result = await _orderManagementService.DownloadAttachmentsZipAsync(id);

            if (result.IsSuccess)
            {
                string zipFileName = $"مرفقات_طلب_{id}.zip";
                return File(result.Data, "application/zip", zipFileName);
            }
            else
            {
                TempData["ErrorMessage"] = result.Message;
                return RedirectToAction(nameof(Index));
            }

        }
    }
}