using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Interfaces;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Web.ViewModels;
using Microsoft.Extensions.Logging;
using OrderFlowCore.Web.Attributes;
using OrderFlowCore.Core.Entities;
using Microsoft.AspNetCore.Mvc.Rendering;

namespace OrderFlowCore.Web.Controllers
{
    [AuthorizeRole(UserRole.DirectManager, UserRole.AssistantManager, UserRole.Coordinator, UserRole.Supervisor, UserRole.Manager, UserRole.Admin)]
    public class EmployeeController : Controller
    {
        private readonly IEmployeeService _employeeService;
        private readonly IDepartmentService _departmentService;
        private readonly IJobTypeService _jobTypeService;
        private readonly INationalityService _nationalityService;
        private readonly IEmploymentTypeService _employmentTypeService;
        private readonly IQualificationService _qualificationService;
        private readonly ILogger<EmployeeController> _logger;

        public EmployeeController(
            IEmployeeService employeeService,
            IDepartmentService departmentService,
            IJobTypeService jobTypeService,
            INationalityService nationalityService,
            IEmploymentTypeService employmentTypeService,
            IQualificationService qualificationService,
            ILogger<EmployeeController> logger)
        {
            _employeeService = employeeService;
            _departmentService = departmentService;
            _jobTypeService = jobTypeService;
            _nationalityService = nationalityService;
            _employmentTypeService = employmentTypeService;
            _qualificationService = qualificationService;
            _logger = logger;
        }

        public async Task<IActionResult> Index()
        {
            var result = await _employeeService.GetAllAsync();
            if (!result.IsSuccess)
            {
                TempData["ErrorMessage"] = result.Message;
                return RedirectToAction("Index", "Home");
            }
            
            return View(result.Data);
        }

        public async Task<IActionResult> Create()
        {
            var viewModel = new EmployeeViewModel();
            await PopulateDropdowns(viewModel);
            return View(viewModel);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(EmployeeViewModel viewModel)
        {
            if (!ModelState.IsValid)
            {
                await PopulateDropdowns(viewModel);
                return View(viewModel);
            }

            var dto = new EmployeeDto
            {
                Name = viewModel.Name,
                Job = viewModel.Job,
                EmployeeNumber = viewModel.EmployeeNumber,
                CivilNumber = viewModel.CivilNumber,
                Nationality = viewModel.Nationality,
                Mobile = viewModel.Mobile,
                EmploymentType = viewModel.EmploymentType,
                Qualification = viewModel.Qualification,
                Department = viewModel.Department
            };

            var result = await _employeeService.CreateAsync(dto);
            if (result.IsSuccess)
            {
                TempData["SuccessMessage"] = result.Message;
                return RedirectToAction(nameof(Index));
            }
            else
            {
                TempData["ErrorMessage"] = result.Message;
                await PopulateDropdowns(viewModel);
            }

            return View(viewModel);
        }

        public async Task<IActionResult> Edit(int id)
        {
            var result = await _employeeService.GetByIdAsync(id);
            if (!result.IsSuccess)
            {
                TempData["ErrorMessage"] = result.Message;
                return RedirectToAction(nameof(Index));
            }

            var viewModel = new EmployeeViewModel
            {
                Id = result.Data.Id,
                Name = result.Data.Name,
                Job = result.Data.Job,
                EmployeeNumber = result.Data.EmployeeNumber,
                CivilNumber = result.Data.CivilNumber,
                Nationality = result.Data.Nationality,
                Mobile = result.Data.Mobile,
                EmploymentType = result.Data.EmploymentType,
                Qualification = result.Data.Qualification,
                Department = result.Data.Department
            };

            await PopulateDropdowns(viewModel);
            return View(viewModel);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(EmployeeViewModel viewModel)
        {
            if (ModelState.IsValid)
            {
                var dto = new EmployeeDto
                {
                    Id = viewModel.Id,
                    Name = viewModel.Name,
                    Job = viewModel.Job,
                    EmployeeNumber = viewModel.EmployeeNumber,
                    CivilNumber = viewModel.CivilNumber,
                    Nationality = viewModel.Nationality,
                    Mobile = viewModel.Mobile,
                    EmploymentType = viewModel.EmploymentType,
                    Qualification = viewModel.Qualification,
                    Department = viewModel.Department
                };

                var result = await _employeeService.UpdateAsync(dto);
                if (result.IsSuccess)
                {
                    TempData["SuccessMessage"] = result.Message;
                    return RedirectToAction(nameof(Index));
                }
                else
                {
                    TempData["ErrorMessage"] = result.Message;
                }
            }

            await PopulateDropdowns(viewModel);
            return View(viewModel);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Delete(int id)
        {
            var result = await _employeeService.DeleteAsync(id);
            if (result.IsSuccess)
            {
                TempData["SuccessMessage"] = result.Message;
            }
            else
            {
                TempData["ErrorMessage"] = result.Message;
            }
            return RedirectToAction(nameof(Index));
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ExportToExcel()
        {
            try
            {
                var result = await _employeeService.ExportToExcelAsync();
                if (result.IsSuccess)
                {
                    var fileName = $"الموظفين_{DateTime.Now:yyyy-MM-dd_HH-mm}.xlsx";
                    return File(result.Data, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
                }
                else
                {
                    TempData["ErrorMessage"] = result.Message;
                    return RedirectToAction(nameof(Index));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting employees to Excel");
                TempData["ErrorMessage"] = "حدث خطأ أثناء تصدير الموظفين";
                return RedirectToAction(nameof(Index));
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ImportFromExcel()
        {
            try
            {
                var file = Request.Form.Files["excelFile"];
                if (file == null || file.Length == 0)
                {
                    return Json(new { success = false, message = "يرجى اختيار ملف Excel صالح" });
                }

                // Validate file type
                if (!file.ContentType.Contains("spreadsheetml") && !file.ContentType.Contains("excel"))
                {
                    return Json(new { success = false, message = "يجب أن يكون الملف بصيغة Excel (.xlsx)" });
                }

                // Validate file size (5MB limit)
                if (file.Length > 5242880)
                {
                    return Json(new { success = false, message = "حجم الملف يجب أن يكون أقل من 5 ميجابايت" });
                }

                using var stream = file.OpenReadStream();
                var result = await _employeeService.ImportFromExcelAsync(stream);

                if (result.IsSuccess)
                {
                    return Json(new {
                        success = true,
                        message = result.Message,
                        importedCount = result.Data,
                        errors = result.Errors
                    });
                }
                else
                {
                    return Json(new { success = false, message = result.Message });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error importing employees from Excel");
                return Json(new { success = false, message = "حدث خطأ أثناء استيراد الموظفين" });
            }
        }

        private async Task PopulateDropdowns(EmployeeViewModel viewModel)
        {
            // Populate departments
            var departmentsResult = await _departmentService.GetAllDepartmentsAsync();
            if (departmentsResult.IsSuccess)
            {
                viewModel.Departments = departmentsResult.Data
                    .Select(d => new SelectListItem { Value = d.Name, Text = d.Name })
                    .ToList();
            }

            // Populate job types
            var jobTypesResult = await _jobTypeService.GetAllAsync();
            if (jobTypesResult.IsSuccess)
            {
                viewModel.JobTypes = jobTypesResult.Data
                    .Select(j => new SelectListItem { Value = j.Name, Text = j.Name })
                    .ToList();
            }

            // Populate nationalities
            var nationalitiesResult = await _nationalityService.GetAllAsync();
            if (nationalitiesResult.IsSuccess)
            {
                viewModel.Nationalities = nationalitiesResult.Data
                    .Select(n => new SelectListItem { Value = n.Name, Text = n.Name })
                    .ToList();
            }

            // Populate employment types
            var employmentTypesResult = await _employmentTypeService.GetAllAsync();
            if (employmentTypesResult.IsSuccess)
            {
                viewModel.EmploymentTypes = employmentTypesResult.Data
                    .Select(e => new SelectListItem { Value = e.Name, Text = e.Name })
                    .ToList();
            }

            // Populate qualifications
            var qualificationsResult = await _qualificationService.GetAllAsync();
            if (qualificationsResult.IsSuccess)
            {
                viewModel.Qualifications = qualificationsResult.Data
                    .Select(q => new SelectListItem { Value = q.Name, Text = q.Name })
                    .ToList();
            }
        }
    }
} 