using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using OrderFlowCore.Application.Common;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Web.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace OrderFlowCore.Web.Controllers
{
    [Authorize]
    public class StatisticsController : Controller
    {
        private readonly IStatisticsService _statisticsService;
        private readonly ILogger<StatisticsController> _logger;

        public StatisticsController(
            IStatisticsService statisticsService,
            ILogger<StatisticsController> logger)
        {
            _statisticsService = statisticsService ?? throw new ArgumentNullException(nameof(statisticsService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task<IActionResult> Index()
        {
            try
            {
                var viewModel = new StatisticsViewModel();
                var errorMessages = new List<string>();

                // Execute statistics queries sequentially to avoid DbContext concurrency issues
                // This is safer than parallel execution when using the same DbContext instance

                // Get general statistics
                var generalStatsResult = await _statisticsService.GetGeneralStatisticsAsync();
                ProcessStatisticsResult(generalStatsResult,
                    result => viewModel.GeneralStatistics = result,
                    errorMessages);

                // Get stage statistics
                var stageStatsResult = await _statisticsService.GetStageStatisticsAsync();
                ProcessStatisticsResult(stageStatsResult,
                    result => viewModel.StageStatistics = result,
                    errorMessages);

                // Get department statistics
                var departmentStatsResult = await _statisticsService.GetDepartmentStatisticsAsync();
                ProcessStatisticsResult(departmentStatsResult,
                    result => viewModel.DepartmentStatistics = result,
                    errorMessages);

                // Get supervisor statistics
                var supervisorStatsResult = await _statisticsService.GetSupervisorStatisticsAsync();
                ProcessStatisticsResult(supervisorStatsResult,
                    result => viewModel.SupervisorStatistics = result,
                    errorMessages);

                // Get transfer type statistics
                var transferTypeStatsResult = await _statisticsService.GetTransferTypeStatisticsAsync();
                ProcessStatisticsResult(transferTypeStatsResult,
                    result => viewModel.TransferTypeStatistics = result,
                    errorMessages);

                // Set error messages if any
                if (errorMessages.Any())
                {
                    TempData["ErrorMessage"] = string.Join("; ", errorMessages);
                    _logger.LogWarning("Statistics loaded with errors: {Errors}", string.Join("; ", errorMessages));
                }
                else
                {
                    _logger.LogInformation("Statistics loaded successfully");
                }

                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading statistics");
                TempData["ErrorMessage"] = "حدث خطأ أثناء تحميل الإحصائيات";
                return View(new StatisticsViewModel());
            }
        }

        /// <summary>
        /// API endpoint for getting statistics data as JSON (useful for AJAX calls)
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> GetStatisticsData(string type)
        {
            try
            {
                return type?.ToLower() switch
                {
                    "general" => Json(await _statisticsService.GetGeneralStatisticsAsync()),
                    "stage" => Json(await _statisticsService.GetStageStatisticsAsync()),
                    "department" => Json(await _statisticsService.GetDepartmentStatisticsAsync()),
                    "supervisor" => Json(await _statisticsService.GetSupervisorStatisticsAsync()),
                    "transfertype" => Json(await _statisticsService.GetTransferTypeStatisticsAsync()),
                    _ => BadRequest("Invalid statistics type")
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting statistics data for type: {Type}", type);
                return StatusCode(500, "حدث خطأ أثناء جلب البيانات");
            }
        }

        /// <summary>
        /// Get aggregated statistics for dashboard
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> GetAggregatedStatistics()
        {
            try
            {
                // Execute all statistics calls sequentially
                var generalStats = await _statisticsService.GetGeneralStatisticsAsync();
                var stageStats = await _statisticsService.GetStageStatisticsAsync();
                var departmentStats = await _statisticsService.GetDepartmentStatisticsAsync();

                var result = new
                {
                    General = generalStats.IsSuccess ? generalStats.Data : null,
                    Stages = stageStats.IsSuccess ? stageStats.Data : null,
                    Departments = departmentStats.IsSuccess ? departmentStats.Data : null,
                    Errors = new List<string>()
                        .Concat(generalStats.IsSuccess ? new string[0] : new[] { generalStats.Message })
                        .Concat(stageStats.IsSuccess ? new string[0] : new[] { stageStats.Message })
                        .Concat(departmentStats.IsSuccess ? new string[0] : new[] { departmentStats.Message })
                        .Where(x => !string.IsNullOrEmpty(x))
                        .ToList()
                };

                return Json(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting aggregated statistics");
                return StatusCode(500, new { Error = "حدث خطأ أثناء جلب البيانات المجمعة" });
            }
        }

        /// <summary>
        /// Refresh statistics endpoint for AJAX calls
        /// </summary>
        [HttpPost]
        public async Task<IActionResult> RefreshStatistics()
        {
            try
            {
                // Just return success - the page will reload
                // You could implement cache clearing here if needed
                await Task.Delay(100); // Simulate some processing time

                return Json(new { success = true, message = "تم تحديث الإحصائيات بنجاح" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error refreshing statistics");
                return Json(new { success = false, message = "حدث خطأ أثناء تحديث الإحصائيات" });
            }
        }

        /// <summary>
        /// Helper method to process service results and handle errors consistently
        /// </summary>
        private static void ProcessStatisticsResult<T>(
            ServiceResult<T> result,
            Action<T> onSuccess,
            List<string> errorMessages)
        {
            if (result.IsSuccess && result.Data != null)
            {
                onSuccess(result.Data);
            }
            else
            {
                errorMessages.Add(result.Message ?? "حدث خطأ غير معروف");
            }
        }
    }
}