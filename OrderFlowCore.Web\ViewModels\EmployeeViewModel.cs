using Microsoft.AspNetCore.Mvc.Rendering;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace OrderFlowCore.Web.ViewModels
{
    public class EmployeeViewModel
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "اسم الموظف مطلوب")]
        [StringLength(100, ErrorMessage = "اسم الموظف يجب أن لا يتجاوز 100 حرف")]
        [Display(Name = "اسم الموظف")]
        public string Name { get; set; }

        [Required(ErrorMessage = "الوظيفة مطلوبة")]
        [StringLength(100, ErrorMessage = "الوظيفة يجب أن لا تتجاوز 100 حرف")]
        [Display(Name = "الوظيفة")]
        public string Job { get; set; }

        [Required(ErrorMessage = "رقم الموظف مطلوب")]
        [StringLength(50, ErrorMessage = "رقم الموظف يجب أن لا يتجاوز 50 حرف")]
        [Display(Name = "رقم الموظف")]
        public string EmployeeNumber { get; set; }

        [Required(ErrorMessage = "السجل المدني مطلوب")]
        [StringLength(50, ErrorMessage = "السجل المدني يجب أن لا يتجاوز 50 حرف")]
        [Display(Name = "السجل المدني")]
        public string CivilNumber { get; set; }

        [Required(ErrorMessage = "الجنسية مطلوبة")]
        [StringLength(50, ErrorMessage = "الجنسية يجب أن لا تتجاوز 50 حرف")]
        [Display(Name = "الجنسية")]
        public string Nationality { get; set; }

        [Required(ErrorMessage = "رقم الجوال مطلوب")]
        [StringLength(15, ErrorMessage = "رقم الجوال يجب أن لا يتجاوز 15 حرف")]
        [Display(Name = "رقم الجوال")]
        public string Mobile { get; set; }

        [Required(ErrorMessage = "نوع التوظيف مطلوب")]
        [StringLength(50, ErrorMessage = "نوع التوظيف يجب أن لا يتجاوز 50 حرف")]
        [Display(Name = "نوع التوظيف")]
        public string EmploymentType { get; set; }

        [Required(ErrorMessage = "المؤهل مطلوب")]
        [StringLength(50, ErrorMessage = "المؤهل يجب أن لا يتجاوز 50 حرف")]
        [Display(Name = "المؤهل")]
        public string Qualification { get; set; }
        [Required(ErrorMessage = "القسم مطلوب")]
        [StringLength(50, ErrorMessage = "القسم يجب أن لا يتجاوز 50 حرف")]
        [Display(Name = "القسم")]
        public string Department { get; set; }

        // Dropdown lists
        public List<SelectListItem> Departments { get; set; } = new List<SelectListItem>();
        public List<SelectListItem> JobTypes { get; set; } = new List<SelectListItem>();
        public List<SelectListItem> Nationalities { get; set; } = new List<SelectListItem>();
        public List<SelectListItem> EmploymentTypes { get; set; } = new List<SelectListItem>();
        public List<SelectListItem> Qualifications { get; set; } = new List<SelectListItem>();
    }
} 