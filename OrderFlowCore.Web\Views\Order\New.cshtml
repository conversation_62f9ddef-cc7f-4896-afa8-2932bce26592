@model OrderFlowCore.Web.ViewModels.OrderNewViewModel
@{
    ViewData["Title"] = "تقديم طلب جديد";
    Layout = "_Layout";
}

<!-- Hero Section -->
<section class="hero-contact">
    <div class="container text-center text-white position-relative">
        <h1 class="display-3 fw-bold mb-4 animate-fade-in">تقديم طلب جديد</h1>
        <p class="lead fs-4 mb-0 animate-fade-in" style="animation-delay: 0.2s;">املأ النموذج أدناه لتقديم طلبك بسهولة وسرعة</p>
        
        <!-- Floating shapes -->
        <div class="floating-shape" style="top: 20%; right: 10%; width: 100px; height: 100px; background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, transparent 70%); border-radius: 50%;"></div>
        <div class="floating-shape" style="bottom: 30%; left: 5%; width: 80px; height: 80px; background: radial-gradient(circle, rgba(255,255,255,0.15) 0%, transparent 70%); border-radius: 50%; animation-delay: -3s;"></div>
    </div>
</section>

<!-- Order Form Section -->
<section class="py-5" style="margin-top: -150px;">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="contact-form-card p-5">
                    <div class="text-center mb-5">
                        <span class="badge bg-primary bg-opacity-10 text-primary px-3 py-2 mb-3">طلب جديد</span>
                        <h2 class="display-6 fw-bold">معلومات الموظف</h2>
                        <p class="text-muted">يرجى ملء جميع الحقول المطلوبة بدقة</p>
                    </div>

                    <!-- Employee Search Section -->
                    <div class="card mb-4 border-0 shadow-sm">
                        <div class="card-body">
                            <div class="text-center mb-3">
                                <h6 class="fw-bold text-primary">
                                    <i class="fas fa-search me-2"></i>البحث عن الموظف
                                </h6>
                                <p class="text-muted small">أدخل السجل المدني للبحث عن بيانات الموظف</p>
                            </div>
                            <div class="row g-3 align-items-end">
                                <div class="col-md-8">
                                    <label class="form-label-modern-dark small">السجل المدني</label>
                                    <input type="text" id="searchCivilRecord" class="form-control form-control-modern" placeholder="أدخل السجل المدني" />
                                </div>
                                <div class="col-md-4">
                                    <button type="button" id="searchEmployeeBtn" class="btn btn-modern btn-primary-modern w-100">
                                        <i class="fas fa-search me-2"></i>بحث
                                    </button>
                                </div>
                            </div>
                            <div id="searchResult" class="mt-3"></div>
                        </div>
                    </div>

                    @if (TempData["OrderSuccess"] != null && TempData["OrderId"] != null)

                    {
                        <div class="success-message-panel" style="background: #e6fff2; border: 1px solid #b2f5ea; padding: 24px; border-radius: 12px; margin-bottom: 24px; text-align: center;">
                            <span class="success-title" style="font-size: 1.5rem; font-weight: bold; color: #38a169;">تم تقديم الطلب بنجاح</span>
                            <div class="order-details" style="margin-top: 12px;">
                                رقم الطلب / Order ID:
                                <span class="order-number" style="font-weight: bold; color: #3182ce;">@TempData["OrderId"]</span>
                                <br />
                                <span class="success-note" style="color: #2d3748;">تم تقديم الطلب بنجاح وتم توجيهه إلى مدير القسم</span>
                            </div>
                        </div>
                    }

                    @if (TempData["SuccessMessage"] != null)
                    {
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            @TempData["SuccessMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    }

                    @if (TempData["ErrorMessage"] != null)
                    {
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            @TempData["ErrorMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    }
                    
                    <form asp-action="New" method="post" enctype="multipart/form-data" id="orderForm">
                        <div asp-validation-summary="ModelOnly" class="alert alert-danger"></div>

                        <div class="row g-4">
                            <div class="col-md-6">
                                <label class="form-label-modern-dark">اسم الموظف <span class="text-danger">*</span></label>
                                <input asp-for="EmployeeName" class="form-control form-control-modern" required />
                                <span asp-validation-for="EmployeeName" class="text-danger"></span>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label-modern-dark">الوظيفة <span class="text-danger">*</span></label>
                                <select asp-for="JobTitle" asp-items="Model.JobTitles" class="form-select form-select-modern" required>
                                    <option value="">-- اختر الوظيفة --</option>
                                </select>
                                <span asp-validation-for="JobTitle" class="text-danger"></span>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label-modern-dark">رقم الموظف <span class="text-danger">*</span></label>
                                <input asp-for="EmployeeNumber" class="form-control form-control-modern" required />
                                <span asp-validation-for="EmployeeNumber" class="text-danger"></span>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label-modern-dark">السجل المدني <span class="text-danger">*</span></label>
                                <input asp-for="CivilRecord" class="form-control form-control-modern" required />
                                <span asp-validation-for="CivilRecord" class="text-danger"></span>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label-modern-dark">الجنسية <span class="text-danger">*</span></label>
                                <select asp-for="Nationality" asp-items="Model.Nationalities" class="form-select form-select-modern" required>
                                    <option value="">-- اختر الجنسية --</option>
                                </select>
                                <span asp-validation-for="Nationality" class="text-danger"></span>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label-modern-dark">رقم الجوال <span class="text-danger">*</span></label>
                                <input asp-for="MobileNumber" class="form-control form-control-modern" required placeholder="+966 5X XXX XXXX" />
                                <span asp-validation-for="MobileNumber" class="text-danger"></span>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label-modern-dark">القسم <span class="text-danger">*</span></label>
                                <select asp-for="Department" asp-items="Model.Departments" class="form-select form-select-modern" required>
                                    <option value="">-- اختر القسم --</option>
                                </select>
                                <span asp-validation-for="Department" class="text-danger"></span>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label-modern-dark">نوع التوظيف <span class="text-danger">*</span></label>
                                <select asp-for="EmploymentType" asp-items="Model.EmploymentTypes" class="form-select form-select-modern" required>
                                    <option value="">-- اختر نوع التوظيف --</option>
                                </select>
                                <span asp-validation-for="EmploymentType" class="text-danger"></span>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label-modern-dark">المؤهل العلمي <span class="text-danger">*</span></label>
                                <select asp-for="Qualification" asp-items="Model.Qualifications" class="form-select form-select-modern" required>
                                    <option value="">-- اختر المؤهل --</option>
                                </select>
                                <span asp-validation-for="Qualification" class="text-danger"></span>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label-modern-dark">نوع الطلب <span class="text-danger">*</span></label>
                                <select asp-for="OrderType" asp-items="Model.OrderTypes" class="form-select form-select-modern" required>
                                    <option value="">-- اختر نوع الطلب --</option>
                                </select>
                                <span asp-validation-for="OrderType" class="text-danger"></span>
                            </div>
                            <div class="col-12">
                                <label class="form-label-modern-dark">تفاصيل الطلب <span class="text-danger">*</span></label>
                                <textarea asp-for="Details" class="form-control form-control-modern" rows="4" required placeholder="اكتب تفاصيل طلبك هنا..."></textarea>
                                <span asp-validation-for="Details" class="text-danger"></span>
                            </div>
                            <div class="col-12">
                                <label class="form-label-modern-dark">المرفقات</label>
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <label class="form-label-modern-dark small">المرفق الأول</label>
                                        <input type="file" class="form-control form-control-modern" accept=".pdf" name="Attachments" />
                                        <div class="form-text small">ملف PDF</div>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label-modern-dark small">المرفق الثاني</label>
                                        <input type="file" class="form-control form-control-modern" accept=".pdf" name="Attachments" />
                                        <div class="form-text small">ملف PDF</div>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label-modern-dark small">المرفق الثالث</label>
                                        <input type="file" class="form-control form-control-modern" accept=".pdf" name="Attachments" />
                                        <div class="form-text small">ملف PDF</div>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label-modern-dark small">المرفق الرابع</label>
                                        <input type="file" class="form-control form-control-modern" accept=".pdf" name="Attachments" />
                                        <div class="form-text small">ملف PDF</div>
                                    </div>
                                </div>
                                <div class="form-text mt-2">يمكن رفع ملفات PDF فقط، الحد الأقصى 4 ملفات</div>
                                <span asp-validation-for="Attachments" class="text-danger"></span>
                            </div>
                            <div class="col-12 text-center mt-5">
                                <button type="submit" class="btn btn-send">
                                    <i class="fas fa-paper-plane me-2"></i>تقديم الطلب
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div> 
    </div>
</section>

<!-- Information Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="text-center mb-5">
            <span class="badge bg-primary bg-opacity-10 text-primary px-3 py-2 mb-3">معلومات مهمة</span>
            <h2 class="display-6 fw-bold">تعليمات تقديم الطلب</h2>
            <p class="text-muted">يرجى قراءة التعليمات التالية قبل تقديم طلبك</p>
        </div>
        <div class="row g-4">
            <div class="col-md-4">
                <div class="contact-card text-center">
                    <div class="contact-card-icon" style="background: rgba(8, 145, 178, 0.1);">
                        <i class="fas fa-file-alt" style="color: var(--primary-color);"></i>
                    </div>
                    <h5 class="fw-bold mb-3">المستندات المطلوبة</h5>
                    <p class="text-muted">تأكد من إرفاق جميع المستندات المطلوبة بصيغة PDF</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="contact-card text-center">
                    <div class="contact-card-icon" style="background: rgba(16, 185, 129, 0.1);">
                        <i class="fas fa-clock" style="color: var(--success);"></i>
                    </div>
                    <h5 class="fw-bold mb-3">وقت المعالجة</h5>
                    <p class="text-muted">يتم معالجة الطلبات خلال 2-5 أيام عمل</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="contact-card text-center">
                    <div class="contact-card-icon" style="background: rgba(245, 158, 11, 0.1);">
                        <i class="fas fa-bell" style="color: var(--warning);"></i>
                    </div>
                    <h5 class="fw-bold mb-3">المتابعة</h5>
                    <p class="text-muted">ستتلقى إشعارات بكل تحديث على حالة طلبك</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Back to Home -->
<section class="py-4 bg-light">
    <div class="container text-center">
        <a href="@Url.Action("Index", "Home")" class="btn btn-modern btn-primary-modern" style="background: var(--primary-color); color: white;">
            <i class="fas fa-arrow-right me-2"></i>العودة للصفحة الرئيسية
        </a>
    </div>
</section>



@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        // Form validation and submission
        document.getElementById('orderForm').addEventListener('submit', function(e) {
            // Add validation classes
            if (!this.checkValidity()) {
                e.stopPropagation();
                this.classList.add('was-validated');
                return;
            }
            
            // Show loading state
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري التقديم...';
            submitBtn.disabled = true;
        });

        // Phone number formatting
        const phoneInput = document.querySelector('input[asp-for="MobileNumber"]');
        if (phoneInput) {
            phoneInput.addEventListener('input', function(e) {
                let value = e.target.value.replace(/\s/g, '');
                let formattedValue = '';
                
                if (value.startsWith('+966')) {
                    if (value.length > 4) formattedValue += value.substring(0, 4) + ' ';
                    if (value.length > 5) formattedValue += value.substring(4, 6) + ' ';
                    if (value.length > 6) formattedValue += value.substring(6, 9) + ' ';
                    if (value.length > 9) formattedValue += value.substring(9, 13);
                } else {
                    formattedValue = value;
                }
                
                e.target.value = formattedValue.trim();
            });
        }

        // Floating shapes parallax
        window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset;
            const shapes = document.querySelectorAll('.floating-shape');
            
            shapes.forEach((shape, index) => {
                const speed = 0.5 + (index * 0.2);
                shape.style.transform = `translateY(${scrolled * speed}px) rotate(${scrolled * 0.1}deg)`;
            });
        });

        // Contact cards interactive effect
        const contactCards = document.querySelectorAll('.contact-card');
        contactCards.forEach(card => {
            card.addEventListener('mousemove', function(e) {
                const rect = this.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;
                
                const centerX = rect.width / 2;
                const centerY = rect.height / 2;
                
                const rotateX = (y - centerY) / 10;
                const rotateY = (centerX - x) / 10;
                
                this.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) translateZ(10px)`;
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = '';
            });
        });

        // Employee search functionality
        const searchBtn = document.getElementById('searchEmployeeBtn');
        const searchInput = document.getElementById('searchCivilRecord');
        const searchResult = document.getElementById('searchResult');

        if (searchBtn && searchInput && searchResult) {
            searchBtn.addEventListener('click', searchEmployee);
            searchInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    searchEmployee();
                }
            });
        }

        function searchEmployee() {
            const civilRecord = searchInput.value.trim();
            if (!civilRecord) {
                showSearchResult('يرجى إدخال السجل المدني', 'warning');
                return;
            }

            // Show loading state
            searchBtn.disabled = true;
            searchBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري البحث...';
            showSearchResult('جاري البحث عن الموظف...', 'info');

            // Make AJAX request
            fetch(`/Order/SearchEmployee?civilRecord=${encodeURIComponent(civilRecord)}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        populateFormFields(data.employee);
                        showSearchResult('تم العثور على الموظف بنجاح!', 'success');
                    } else {
                        showSearchResult(data.message, 'danger');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showSearchResult('حدث خطأ أثناء البحث', 'danger');
                })
                .finally(() => {
                    // Reset button state
                    searchBtn.disabled = false;
                    searchBtn.innerHTML = '<i class="fas fa-search me-2"></i>بحث';
                });
        }

        function populateFormFields(employee) {
            // Populate form fields with employee data
            const fields = {
                'EmployeeName': employee.name,
                'JobTitle': employee.job,
                'EmployeeNumber': employee.employeeNumber,
                'CivilRecord': employee.civilNumber,
                'Nationality': employee.nationality,
                'MobileNumber': employee.mobile,
                'EmploymentType': employee.employmentType,
                'Qualification': employee.qualification,
                'Department': employee.department,
            };

            Object.keys(fields).forEach(fieldName => {
                const field = document.getElementById(fieldName);
                if (field && fields[fieldName]) {
                    field.value = fields[fieldName];
                    // Trigger change event
                    field.dispatchEvent(new Event('change', { bubbles: true }));
                }
            });
        }

        function showSearchResult(message, type) {
            const alertClass = type === 'success' ? 'alert-success' : 
                              type === 'warning' ? 'alert-warning' : 
                              type === 'info' ? 'alert-info' : 'alert-danger';
            
            const iconClass = type === 'success' ? 'check-circle' : 
                             type === 'warning' ? 'exclamation-triangle' : 
                             type === 'info' ? 'info-circle' : 'times-circle';
            
            searchResult.innerHTML = `
                <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                    <i class="fas fa-${iconClass} me-2"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
        }
    </script>
}  