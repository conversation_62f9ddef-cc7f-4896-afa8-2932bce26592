{"GlobalPropertiesHash": "Bfyky2FCFFxmA0I8C+MiI7GsdzAuWb6eQqbi3zQLxLQ=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["YVasURxL2jT67Xbcs4I+S6M0BDetPYkzydijgxdBC9c=", "PXGczEHwRGhx4bQseVCb3q7MSPala6X0DwRptM6uRjc=", "8tMsGoFHiEyTX6Hdhgubeq0FwItOh5uaiMwL7UGtxfA=", "tyc99F5214MJLopPUL75LoLvcgEn9x/tFi6LjB1vRs0=", "QNyp1AgPC2VxT/IPw4vFaoX6wKhkPtE02UdxJKkIxIQ=", "MVlg+z8WSC7ufBbJ5N6n8jcCOQrAiet3KTIrWNRpD9o=", "DRgY2M224okKkAsZaSWg4qqz22u/ItLt2x1Lan3Gi0o=", "KfdivS3rIXzka8pqm91DdZxlDG62fOI2gebOT3mKm7I=", "jAMyC2LElYg+iwV6Fet6y/PdYniu5CF2fYRl5ONZyZk=", "2Z2PwzjX2EtpAuZcv8fbzRI3dLkoThdevWeRfmnBgZY=", "s5xl46AuqaOwwVQOlNxL7kcRODi0U+3rzR77YqS5T34=", "SLtUNOmmU3fYUMuaJNfgCzQpTWi6ZQolcoQ2Le3s97A=", "lyypl0CLgXGxLYMPIAJ/BFrb+WsxRtqkII1PI1KGXZU=", "lBQ/l1i8bertNKtETFhNWjeAcV7/tQpTvlYSjiHWqbY=", "1eYTEtPR3dpd4Mlib1K0JAyG5UGD0ZTrDDjsHOtok2w=", "TBWPX4g1ibJZT6Ogt2GHS7i9ZTKRkti6QBeBMJcLaSc=", "OuGUo5LJbjnPoJqxlMjBhCzt0PgCFnm6FkxvJ+XLeic=", "MMVCLYtjJJVRDKNHkI7B7oGUbw2TuNaaY6tWRYfvYb4=", "+gkzRJwQZdAlZhW1rr4h1VIcfYfK2OARiG/MjrogXVQ=", "FaX5du/IS6uLUS/0dlSL8x1YdOe/B0aD7dCQy3YWln8=", "HyGnE3/qi1/rcIDI3n2c9lGJHHdfH1NdcTS5OIgR4oM=", "JJ+T1xD3Jd0onDka0LcxD3XmpvJbPIYD9jL0dGCO5iE=", "nmeP5xMMyhWplZMQClN6nh+ZyiceVkOIgPFzIs4oJAU=", "KShbojwqsuVuBXYoYtaNM5nYHlY7greUtdBz9w0W6Qo=", "UEBmQRFp99hDA4ayHKJ0ECxE5b7M6QwZbEp59JdblxY=", "NrQJUImVTL5QzwT5dz3CJqEMrZrwfi9uvxM/+DlZ1cs=", "E+AOJLIgM9S1plBPZR1QWfXziRVPJc/ibdlb+JlsOw8=", "KYN2ylTO7LtUC7ax9OowUbOXy2GHFKiT9lZaHhRoY4M=", "a1HFQ4mCM1iL9xyVKz102OJUzJQ3ZbqbN2CwAxJYMyo=", "o6+KiWg9nye5dcn7jdn+t2rKCNUZlIJuZvNcX1YliuY=", "iX0ftjacqfbJB6uuce1JZcyEn6yKqfRAKMtlO/DEiG4=", "Lfryg9iOwXy1vkHkS6rVM6beBt2BielLDVk54G2fh9c=", "IHdHY5+9aFqNgryw1y0LXEZsq+VYwhdKpx2N+zKoO/Y=", "m+v2m0O/pO9UYkM6HNEDmmcc9x9TvG9F8qboHceKPJI=", "SHv3ScVw24grUWt4nefaFOgdyKWPUOENyf0xn9X9u/U=", "ld8qIbkC0NlHPFLJODyToL02enBZPRayvKoX4KfYY3Q=", "cUt7xH6XvENHdShZHOcMgt90l1JtUgYQADbMEJCpWOU=", "uBAZnAhwSQ8qaGCT8/oj2x4CbQSsLsgLvTOkgD0rDqo=", "lkUdemlPb40SsTTdjF/873W6i1DmQ2aEOXmyDOrm7Zg=", "oi9z1GVRu6vmju1xFo+yWvRT4BHK0DKKa0W9ShlRsks=", "YuJI4llngV3rmg/ZxLblhrNvkZIQs9kdPQaOnTD0XCs=", "Obbz5ELi3L3kmMHAiJTJ6xwyKQtKLDmAA0TrYU7AWG0=", "uCGGhz7mRfYvdBRvFP8GMkKtMRp2QPnsPboc+OG2aIQ=", "RikoFhRwzzPwEoPlk6srdMa9MdVw1o3M+O3GS6T+g9Q=", "E4XpVkYqesRkvcT/hpLjtTkq42M5nPGa9PZu1dt1eaA=", "pgXNFGRAZQlqRFg1pcHNnGOOLA56pxieYpF4ZmIWwFg=", "4WrMXP0+bEIvqdqytvBpmNerGKnJTGqBhdMvzUfLXRs=", "e23JlzphNt1xx/CguEYQZ+CkK4rLQgHmMWwm3LYzvF0=", "O028ka5sYH8PzwYMMgwlXsSQULdZHGgWa2McTp0I37Q=", "fgMeb2BiKgQj7+ezxgh0fI8ahpves9+CfYj0o0dLdP4=", "97FZhD7eOZr6DvF0PPE/i1hvsrKVmSiQQc79SLkntk8=", "qVW4oeK9nEYTMdsyKXyU+O7AOCDNXWEe67lWD8QPDF8=", "0kFXo6GDThqcdFGIeMw5mUIqfzWQMnSaTsSWOSonVx0=", "+eCRNcjV1KPRZZwo7LLzu3xCSlCqGLKSD/V24+zQ2Zw=", "+EapEPnMgazm9Tv/a2XYmEJqwstNwkOla9hNBMawH4M=", "puXAVD6VdBV5nqxyEHQ26+Csu2TB6anMuk8UhA4MHSk=", "J5g+kTQqZ42mCPzJ4WJA2saAkC1I0lqyTsPaBbF4NDU=", "d+h7rhgMQH4kZZXaPpNTi3UNiFhaX9OJZuR+JPfVzeA=", "sl58ShrNSuvOX/QS5u8qw+rnGNHNqUoky4SWtb50v0I=", "Dr2z4Hv2r8DTpAetw1U1TyCI5XOU98W54bWr1sB5RmU=", "ijkIJQj6fBSfwx8fYzbGPPhXnw86UQnqixq5GbXaQy8=", "uGPIcUUeFFC4QPjYZzcVwHG8LaMDwC1ibcI55sU/aCE=", "pjz7IQ6b87snBm85p8MA7Rq/ALeVam12ET7aZi+DrQs=", "85VmO6i684TIdROe/gGMuXVbcE+bootEi+6bLiAeOa4=", "HxATJyRa4I9ghyUvlekZG3T3atzQYgGZdMqshWy1YjA=", "BbKADuko7Z7auTpgpLLK6Q8YXsD97pmWGaYOeIqxNAQ=", "4d5VmB81298o323vwgFv3w7IEnaFiQmaRRxrDcXk8gU=", "FTmMQvv/j+Wg36tigSsFlu+uO2dagqRbCR0cHUxnrBc=", "rqOV3kMYu/ZtQ0ME60Dn+lOpXjJFmxtl6OmWT3r8wwo=", "f5o2dC09xpPexP5Lo8sHxfdzDRhwRqeEFMdbJIaaxK0=", "0P+Dr2N3uZfcZWgrw1tmvGE04ygNrfwoskw9vETdyhM=", "JFTwU4gmvIi0hcMne2F97/e4eJA+iUcq8TsNXLejYBQ=", "TO4cG3vaFQmF5hJDEV4LFMZ+vDQoh332x9YMyJQWB+8=", "dEpNTlMOk2q7F/OBPUH+ygwJBmeebkOgQUeBiBV8RBQ=", "//og4qIxB4z+u9nW2pb9LFdr3s1dXTDYD2xX0FFBvVs=", "Fj1MMgC8wC7miQgMpf4z1jZJt8Jc7uOx4OLWKIsONS8=", "5YItNRpR4lI+g1BK+IscLWZXWteGhNqJW227VhrTSXg=", "osZzPhgM4zlZEmjtrzHpZnqlkLk1pTjn48T1e0mdP5I=", "uWvKyI0eV2kDDhYnPXrNIY8QjjtOBPqTa59G1gSlEjc=", "fxXaZkDLEFvshO+ZtoCJTPQ4erMr6L3IZ068p7B1d7Q=", "syaGvqVTca5dU9CGI/XVskpXrrs3K7cRQKhYZfm5E7E=", "R5t0cnO2T44O/tvnJl0pzB+dEiRzO4rjRdLpMBI7xkg=", "7NKEJz6yjy9goIEJEhgPitWmS0QaJ55q7TWwm3VUROo=", "jFAzXXxPcAgv0j8+Lz8SGGTWEKAuJBZf4hXLpE5Oonw=", "YW1EdF/TDLf4QTiFNDiSTwHdQOR6HoG4cnHpTryem/c=", "GBA7uA+olu0C4V99OZR5Kj/VvLlzdlcaYSaTEdmgmm8=", "lHw7AR0G1P1/Quqlqw/uUUQOIWvuweygnE27mBmN7YI=", "vcU+fGJf4Qk6L/yi/c7KxKYwK2GK53y8VNYbEX/7HXM=", "0Opdn2H+7zDVIVFmIeL44zIithIcFxdCpYEieKpP41Y=", "ZbBFBHMJPAEwvm6B7kttFEEw1j44s7kqVuzWyAv96pg=", "3RweD1UR8/Al2SEj7OTJbJ5AVqi03wWupO5KB3BqpSo=", "zE+0cpUUz3X2BVSVPnVQ2nnm8GNu9LqK2iH+wvRQq5s=", "CgCcmxcz5a6vkUj6IMvK778Kxn+D0/Rfnc5DSvA6Y2Y=", "tBlPSYWrkKNMYh0QXLRLoSas/A8AYhKP2u8dHILwMiM=", "t+4hxzxKsLBP104tYsSaaOHsc6FmQFZy3Eta2mDBMt8=", "0hpvsGdnN1ORCDsTZVSFcjjma14gKV1AKIFyYKX8eIA=", "ZcHRLUcjrdw5neeu89GFoFjDtBqKTK0ptO5Iw3aFHBo=", "EGhMfmJ1fIaZp4wBt8uzKvyr4QGKcugj96rXTFa/gEE=", "shOBipwDr23GPha7z7BeWBzTuIm8zgpqdKfMQhqsjd0=", "icH+3pBxzUOPShIJv+zdpbZUmr5YdeWF3i0XvDh33H4=", "w7bUgjVksOR8AcsDMravWuwELf6TMJTfdAYoA5fSca8=", "jhIt0XrWtikCJ/OC+E+7NtLswVlWwaU1CupqpNQrqWw=", "YsqaXedXHgjSotNsa3SxQuhBijkNrqQMFANBiNEedLg=", "6Ud2EvQChXZooqOhg3EQnb6xbDU/kBJYPG9CYosJ8u8=", "ucTCrtM/0O+brXa1XDali5JDKnRAVXboKxWm7ft4fMg=", "dqg2Dg9CaoAvezQm8BDHxSYO4ML54yq7HUjUzUBdURY=", "IrefN6tDfzxYHyIzSVW7zBlkzfap0FkbBk2p4QuHmQo=", "kGWqiokH0HGeYtTxx8ZTEW0Swa5H0ekGwWEq6bz5XG4=", "5KZONqJbOivS7CJo1Xmqr/dKW9N0gYlAlNr/d1zv7Co=", "LxlZOIr8cVot8J8zXiDVF4GUgrGSpxlbC5kEc/jFsOg=", "QZUoBBeQ0kH4tviKJjbn7DuDogbXAdKsE/iqKoepaE8=", "mlGDx7JhNdX/QfxvoAO+b7Pgj7wNIXbs8681y4gKXPw=", "gJTp8k5AccV99iTYlyM1tmCdlfZ4qUa9GfNDucxByb0=", "Rlle2MWEjcAQchTvso+zAUhUNiIbZ40p2Az/NtW8PIU=", "I2Qrlke2taqNQ53tB5Av2+Nh68WuGl1iZb3UlyTZ4zE=", "3OUnwBgcKBHiYgTJ78p9Rtk4Peb8XVUpMK8GXegG+PU=", "H4ubpnlwtTQhbJdk46QKvcXBhcs1xNZjBqcW29LV5R8=", "Y5zUa8bRXy7z2kVJSkCD2x39tK151Bo0qraVnFrbDME=", "Ud6UjsnHl74aP81CE8K8oW8BfcOvvvHD0zC47JY2yGs=", "yM1UDPt6eDd823FobaNqul1N3skbz82LkAGwwY7Y6EE=", "x/WjUf1fNS6o8az8md9Hk1PI9Y/9E0qTP9PQHYIYjLQ=", "a/SfI5dBFsR2D56R+jl3NdXLjJBi5lrdISQ/DkOt6Bc=", "HVoIqWsGZdZyCmv9x/EsRb2ReT0qjw7Nc8hQc9eBdR0=", "54nJ3E+fYlmklFlYvT1dn/brFIZZSC8novDtuEhZR5k=", "K4H89dkxqKR+shr57h3dvp9OBB9m45DJAQ2nMFJxgOE=", "teGuVoqjZPzf+Vf2eAI7UVC1yeZxyf6cXefafvoKoSY=", "prOeAnlRMRMyTzhEdlouhy5n/UsRtwaLhb4mKIuqF2M=", "dHkxQFF02W3vd6UmvgqZP8OffEac0RDB29ElPKekaN8=", "da/1GTfJ+uMjjWnWV0ve6g7uFG9gaCJW8uBQ4qLRnM4=", "YxaX7UXUz5TWLwXVbPR32EeUdkEHAd4kJJvfvF2a3to=", "GwDqwa+Yu7XMP+1dq8nGFadxXBuszwwlXvT4lb7PccY=", "GQM0/r43/qQcYAlt5uFgqY1bFn8fd2zz3CPtQEwbYnQ=", "YNSOyOYGLkt8HSvoyUNj3jaqKPI9IHlYiewe2tuzfSM=", "I3SG4lnSeiEqceX++F6BzneaSRGelkhfd898LTnFuug=", "RL/ktSUrD0g01s5xTnwog583UDjErMvtH03w8W3dc+Y=", "kYjxHJbzxwu5r/LvQwQWDeDJkUPYZR+JRwOj0RXRF28=", "gugOT7GW23rlEfY+nnY9dv5QIRLsbqEw3FK/HAU9sVQ=", "L+Vu3RGbTBKvMN9QnQdjF3NzDNJHtZSbCglm/5uS2mI=", "BnfjndHOFmEzkCjoKNPh8rt/MRLc79URYYwRcfwLmOE=", "tu/zKCqrbU8w/GuvnhEVP1oDZr8OjJmtDgQ+Ca4/cSA=", "WizXcM2yVabQYnyOtSyMHl0H1fSGLLBxPUJwo1gGRzs=", "4ooAI0RggLQI5zxXI45JQmSBUdwu8HJzVQ6nFhN6KLU=", "Fx1ZXsqvA0UChf8hx4ixcznRKnEvgm1c6YiRPl5VAhg=", "itwwh2Fn0jYZ5ftXhQk7cS139RBJvb6eyhS01WuC+2Y=", "todfGviUU/MGFdr667PaJ81J2lZ4vlYMG97B5399aqo=", "sUqyjir/xDej3nk6nBK9Rg+fIER6chGecglluXXsNtE=", "Y1FGqS1nN/jvGi2++AuUzPUza2WwZG8wWUEZGJ6oyuI=", "hjekdwGN/b2tRFr6nXFdC8cC4qlqxHf7GioUWlDAJJM=", "qTyrxFwZGAi/VA67m+r8DKd8UQ2cdtPTU+iC0eHAW5k=", "L6SLbKwqhlwbpac3+610O8lxYHwcu8O4HdErgA+gBYo=", "2ltkNrTw82xGwOfOLSAT6ubFHZ88fyR16IUwzNaR8nU=", "mAN9RRIbzT5sPY+Xzn9xTIArJ6D3C/C3KT7F86IofcE=", "o7EadMtLOQ8IeSWPPwQI9XGxNW3zl9bfYFrF2+EEVUc=", "gdIC6a56hWfxw+O12Ku4LgmeeFJoOdm6Lby35/05tT4=", "6eKBgROfrjqjwopJaFf71MWFjjTQEtTzyDwQxyHKr4M=", "GQkFKUzvDthngnOeLMGlj/z70dot7OUEOL2L+c+KWLo=", "euFhV4uSN5vgPRmopmgIfPUCirL/QT2lpCSfSZ9EDuA=", "sAeGy603Ra1+1JvHBLj+KODjVdpn/vbsfW/ev4s8osc=", "db2uZSIFQi5THKEnROUbPu8TLhOFocfAWp0F6mXjHVs=", "3jz1qFH5asoxC3SzhRE6KQ1PGrf2pLnmFyhD/HcHz/Y=", "XnZjJdE7FT/93zZU3BguArftluiAkNdywqaA4MAUFEA=", "bK+Yji7I5EdPCyLM6Dtk4JfSd4qdYxI6TYoYhAxNVq8=", "YRjzH7uRRpayfFPFboprkKS/8x7Ml/a8Zu1evEu8Lnc=", "IKwLLTDNw4vwHFVwZERZmc9atv7xl136ysHPF4aVSDk=", "/7/5+9IUxliEeElwjNfEaZXFcpc31z3Su4Cnahp6tCQ="], "CachedAssets": {"0hpvsGdnN1ORCDsTZVSFcjjma14gKV1AKIFyYKX8eIA=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\uploads\\order_16_147258369_file2.pdf", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "uploads/order_16_147258369_file2#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5h93g0hjve", "Integrity": "R81dFN/nRm/l3oc+HBZ4XWNMmBTvOStB5QNLVcb5CzY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\order_16_147258369_file2.pdf", "FileLength": 1623500, "LastWriteTime": "2025-08-03T21:35:52.4006615+00:00"}, "t+4hxzxKsLBP104tYsSaaOHsc6FmQFZy3Eta2mDBMt8=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\uploads\\order_147258369_file2_20250801235838.pdf", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "uploads/order_147258369_file2_20250801235838#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "clsmlksgek", "Integrity": "wADZjdTvmOukGZw19az5AmpyYwbLMnLIKUy5g54a2Qk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\order_147258369_file2_20250801235838.pdf", "FileLength": 26192, "LastWriteTime": "2025-08-01T20:58:38.8012067+00:00"}, "tBlPSYWrkKNMYh0QXLRLoSas/A8AYhKP2u8dHILwMiM=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\uploads\\order_147258369_file2_20250801235456.pdf", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "uploads/order_147258369_file2_20250801235456#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "clsmlksgek", "Integrity": "wADZjdTvmOukGZw19az5AmpyYwbLMnLIKUy5g54a2Qk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\order_147258369_file2_20250801235456.pdf", "FileLength": 26192, "LastWriteTime": "2025-08-01T20:54:56.4195325+00:00"}, "CgCcmxcz5a6vkUj6IMvK778Kxn+D0/Rfnc5DSvA6Y2Y=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\uploads\\order_147258369_file2_20250801235325.pdf", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "uploads/order_147258369_file2_20250801235325#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "clsmlksgek", "Integrity": "wADZjdTvmOukGZw19az5AmpyYwbLMnLIKUy5g54a2Qk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\order_147258369_file2_20250801235325.pdf", "FileLength": 26192, "LastWriteTime": "2025-08-01T20:53:25.9281749+00:00"}, "zE+0cpUUz3X2BVSVPnVQ2nnm8GNu9LqK2iH+wvRQq5s=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\uploads\\order_147258369_file2_20250801232309.pdf", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "uploads/order_147258369_file2_20250801232309#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "nvt4ecbxj8", "Integrity": "vSGHLgsTUyPJ/44RUV0SRITlmHToPp+9Z8t0HxjLhyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\order_147258369_file2_20250801232309.pdf", "FileLength": 652098, "LastWriteTime": "2025-08-01T20:23:09.739254+00:00"}, "3RweD1UR8/Al2SEj7OTJbJ5AVqi03wWupO5KB3BqpSo=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\uploads\\order_147258369_file1_20250802001459.pdf", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "uploads/order_147258369_file1_20250802001459#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "nvt4ecbxj8", "Integrity": "vSGHLgsTUyPJ/44RUV0SRITlmHToPp+9Z8t0HxjLhyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\order_147258369_file1_20250802001459.pdf", "FileLength": 652098, "LastWriteTime": "2025-08-01T21:14:59.6844669+00:00"}, "ZbBFBHMJPAEwvm6B7kttFEEw1j44s7kqVuzWyAv96pg=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\uploads\\order_147258369_file1_20250801235838.pdf", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "uploads/order_147258369_file1_20250801235838#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "nvt4ecbxj8", "Integrity": "vSGHLgsTUyPJ/44RUV0SRITlmHToPp+9Z8t0HxjLhyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\order_147258369_file1_20250801235838.pdf", "FileLength": 652098, "LastWriteTime": "2025-08-01T20:58:38.800216+00:00"}, "0Opdn2H+7zDVIVFmIeL44zIithIcFxdCpYEieKpP41Y=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\uploads\\order_147258369_file1_20250801235456.pdf", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "uploads/order_147258369_file1_20250801235456#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "nvt4ecbxj8", "Integrity": "vSGHLgsTUyPJ/44RUV0SRITlmHToPp+9Z8t0HxjLhyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\order_147258369_file1_20250801235456.pdf", "FileLength": 652098, "LastWriteTime": "2025-08-01T20:54:56.4175692+00:00"}, "vcU+fGJf4Qk6L/yi/c7KxKYwK2GK53y8VNYbEX/7HXM=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\uploads\\order_147258369_file1_20250801235325.pdf", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "uploads/order_147258369_file1_20250801235325#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "nvt4ecbxj8", "Integrity": "vSGHLgsTUyPJ/44RUV0SRITlmHToPp+9Z8t0HxjLhyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\order_147258369_file1_20250801235325.pdf", "FileLength": 652098, "LastWriteTime": "2025-08-01T20:53:25.926176+00:00"}, "lHw7AR0G1P1/Quqlqw/uUUQOIWvuweygnE27mBmN7YI=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\uploads\\order_147258369_file1_20250801230416.pdf", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "uploads/order_147258369_file1_20250801230416#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "wpwkvdozv9", "Integrity": "XApdrZRO6zTw/32NF7zRhkCkHPVyZIVI1N/Q9PTL0ww=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\order_147258369_file1_20250801230416.pdf", "FileLength": 793786, "LastWriteTime": "2025-08-01T20:04:16.111256+00:00"}, "GBA7uA+olu0C4V99OZR5Kj/VvLlzdlcaYSaTEdmgmm8=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\uploads\\order_1234567890_file1_20250710195413.pdf", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "uploads/order_1234567890_file1_20250710195413#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "9psus3l2z1", "Integrity": "M7v8Td5tSoL2OG4C2tCU6r1OHuKiiInnmu0yL2yEO6w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\order_1234567890_file1_20250710195413.pdf", "FileLength": 326409, "LastWriteTime": "2025-07-10T16:54:13.7093974+00:00"}, "YW1EdF/TDLf4QTiFNDiSTwHdQOR6HoG4cnHpTryem/c=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\uploads\\order_1234567890_file1_20250704150713.pdf", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "uploads/order_1234567890_file1_20250704150713#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "wdbk1wabyj", "Integrity": "pPcNFOHBw4Ef3gPClk6tgsGHZT+tnzHeuazerOEO3wM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\order_1234567890_file1_20250704150713.pdf", "FileLength": 174317, "LastWriteTime": "2025-07-04T12:07:13.879368+00:00"}, "5YItNRpR4lI+g1BK+IscLWZXWteGhNqJW227VhrTSXg=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pzqfkb6aqo", "Integrity": "m0l81WDPiG7CcG7CDsTuZzvcGvyFmrQY5DLIxx3aRGw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 51171, "LastWriteTime": "2025-06-21T20:31:49.5019155+00:00"}, "7NKEJz6yjy9goIEJEhgPitWmS0QaJ55q7TWwm3VUROo=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\jquery\\LICENSE.txt", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mlv21k5csn", "Integrity": "hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 1117, "LastWriteTime": "2025-06-21T20:31:49.7559946+00:00"}, "R5t0cnO2T44O/tvnJl0pzB+dEiRzO4rjRdLpMBI7xkg=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5pze98is44", "Integrity": "OZVI+w57FGwS9boYCZpH1ZSpcP7pYhLu4KtIUvPlZ4I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 137972, "LastWriteTime": "2025-06-21T20:31:49.4969176+00:00"}, "0P+Dr2N3uZfcZWgrw1tmvGE04ygNrfwoskw9vETdyhM=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\LICENSE", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/LICENSE#[.{fingerprint}]?", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "81b7ukuj9c", "Integrity": "ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\LICENSE", "FileLength": 1153, "LastWriteTime": "2025-06-21T20:31:49.7509757+00:00"}, "osZzPhgM4zlZEmjtrzHpZnqlkLk1pTjn48T1e0mdP5I=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b7iojwaux1", "Integrity": "JwUksNJ6/R07ZiLRoXbGeNrtlFZMFDKX4hemPiHOmCA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 24601, "LastWriteTime": "2025-06-21T20:31:49.5019155+00:00"}, "syaGvqVTca5dU9CGI/XVskpXrrs3K7cRQKhYZfm5E7E=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "dd6z7egasc", "Integrity": "/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 89501, "LastWriteTime": "2025-06-21T20:31:49.4929236+00:00"}, "Fj1MMgC8wC7miQgMpf4z1jZJt8Jc7uOx4OLWKIsONS8=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "9oaff4kq20", "Integrity": "N11IyJpHTgDcSCb3AfX4VrBnpGQeem1NoNzzgcXVyCc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 22177, "LastWriteTime": "2025-06-21T20:31:49.5009148+00:00"}, "//og4qIxB4z+u9nW2pb9LFdr3s1dXTDYD2xX0FFBvVs=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ay5nd8zt9x", "Integrity": "4jrcLBsi0Ugm8iLKdqDsAyaCDjkscYZdoGuNH/zqs4E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 52977, "LastWriteTime": "2025-06-21T20:31:49.4999139+00:00"}, "dEpNTlMOk2q7F/OBPUH+ygwJBmeebkOgQUeBiBV8RBQ=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "356vix0kms", "Integrity": "16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 1139, "LastWriteTime": "2025-06-21T20:31:49.7945061+00:00"}, "TO4cG3vaFQmF5hJDEV4LFMZ+vDQoh332x9YMyJQWB+8=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4v8eqarkd7", "Integrity": "YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js", "FileLength": 5824, "LastWriteTime": "2025-06-21T20:31:49.7935058+00:00"}, "JFTwU4gmvIi0hcMne2F97/e4eJA+iUcq8TsNXLejYBQ=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "47otxtyo56", "Integrity": "wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js", "FileLength": 19385, "LastWriteTime": "2025-06-21T20:31:49.7925112+00:00"}, "jFAzXXxPcAgv0j8+Lz8SGGTWEKAuJBZf4hXLpE5Oonw=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\order_printed\\17.pdf", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "order_printed/17#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "gtg9o89r3l", "Integrity": "ZEUCsMqx7q1er333UjU7jvATXU1HSjD2ejSDrMo0dHA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\order_printed\\17.pdf", "FileLength": 335755, "LastWriteTime": "2025-08-03T21:08:03.3839793+00:00"}, "f5o2dC09xpPexP5Lo8sHxfdzDRhwRqeEFMdbJIaaxK0=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "u33ctipx7g", "Integrity": "ui/FQI+y0IUsY8Pbi80b8s3GeEL+PsvdaLTONobpn88=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 217145, "LastWriteTime": "2025-06-21T20:31:49.7049769+00:00"}, "rqOV3kMYu/ZtQ0ME60Dn+lOpXjJFmxtl6OmWT3r8wwo=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "zwph15dxgs", "Integrity": "c4Ll6eSIg6Eothk8pCWAF8aE923EvtU11pqjBy+NjNM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 59511, "LastWriteTime": "2025-06-21T20:31:49.7029791+00:00"}, "FTmMQvv/j+Wg36tigSsFlu+uO2dagqRbCR0cHUxnrBc=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6<PERSON><PERSON><PERSON><PERSON>bh", "Integrity": "Qkl5mZUZ64aYBaORRMP9jfD1kz8J6FwiV2M86JDJkdQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 289522, "LastWriteTime": "2025-06-21T20:31:49.6999777+00:00"}, "4d5VmB81298o323vwgFv3w7IEnaFiQmaRRxrDcXk8gU=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "o4kw7cc6tf", "Integrity": "6IStRQerBchYSw6J2GWTOWGOnDRrWXmaG0r6nCwN5s4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 148168, "LastWriteTime": "2025-06-21T20:31:49.6979767+00:00"}, "BbKADuko7Z7auTpgpLLK6Q8YXsD97pmWGaYOeIqxNAQ=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "wsezl0heh6", "Integrity": "sPqzWcSS9aRa2gpWTVNQzemajn8hrFjgXPj3j9QItQo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 222508, "LastWriteTime": "2025-06-21T20:31:49.6959787+00:00"}, "HxATJyRa4I9ghyUvlekZG3T3atzQYgGZdMqshWy1YjA=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "um2aeqy4ik", "Integrity": "Kj4irQWPwfSb5NFeos/h0IroI5/nIg0HtAjQ+w4v6TE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 72016, "LastWriteTime": "2025-06-21T20:31:49.6919811+00:00"}, "85VmO6i684TIdROe/gGMuXVbcE+bootEi+6bLiAeOa4=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2lgwfvgpvi", "Integrity": "CllC/sbLvyLE9cQljmFRlITfqdZRnBv2ysX5LJtl/dg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 288320, "LastWriteTime": "2025-06-21T20:31:49.6899812+00:00"}, "pjz7IQ6b87snBm85p8MA7Rq/ALeVam12ET7aZi+DrQs=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "m39kt2b5c9", "Integrity": "EuDXUJYKnfZuO8dSLN0f5iVbVasz36AROuAU3NJ3JBo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 139019, "LastWriteTime": "2025-06-21T20:31:49.6879768+00:00"}, "uGPIcUUeFFC4QPjYZzcVwHG8LaMDwC1ibcI55sU/aCE=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2nslu3uf3", "Integrity": "xIBBxDPvWhk8/JdaFEZoejadfaKFUfZFwRS1D4Jkuro=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 327261, "LastWriteTime": "2025-06-21T20:31:49.6869779+00:00"}, "ijkIJQj6fBSfwx8fYzbGPPhXnw86UQnqixq5GbXaQy8=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "25iw1kog22", "Integrity": "KuvCVS19rfTjoLgMyDDCdOkRRlhNrY4psEM4uezts2M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 78468, "LastWriteTime": "2025-06-21T20:31:49.6839761+00:00"}, "Dr2z4Hv2r8DTpAetw1U1TyCI5XOU98W54bWr1sB5RmU=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "kbynt5jhd9", "Integrity": "gO4uhxfGuK0ONjRlHuwfghGfEXT5azm1oHWnTEFGTfk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 425643, "LastWriteTime": "2025-06-21T20:31:49.6829778+00:00"}, "sl58ShrNSuvOX/QS5u8qw+rnGNHNqUoky4SWtb50v0I=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "l2av4jpuoj", "Integrity": "vQTf4d3WJi9vmWQNA4kJnjoedgEhMFXFDEMXqtHtgzk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 208492, "LastWriteTime": "2025-06-21T20:31:49.6799757+00:00"}, "d+h7rhgMQH4kZZXaPpNTi3UNiFhaX9OJZuR+JPfVzeA=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fctod5rc9n", "Integrity": "j7uqK5VoTT4rUHMr911QEU5Sa94lR3uh9E28XBMlzrM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 661035, "LastWriteTime": "2025-06-21T20:31:49.6769754+00:00"}, "J5g+kTQqZ42mCPzJ4WJA2saAkC1I0lqyTsPaBbF4NDU=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "9b9oa1qrmt", "Integrity": "22wR6QTidoeiRZXp6zkRQyMSUb/FB+Av11jqmZJF6uU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 162825, "LastWriteTime": "2025-06-21T20:31:49.6740009+00:00"}, "puXAVD6VdBV5nqxyEHQ26+Csu2TB6anMuk8UhA4MHSk=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4gxs3k148c", "Integrity": "VFvmi/ZSwQFmjS6Pry9B8zXeZ/GA168TXLyykDhNMZE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 536461, "LastWriteTime": "2025-06-21T20:31:49.6719791+00:00"}, "+EapEPnMgazm9Tv/a2XYmEJqwstNwkOla9hNBMawH4M=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ve6x09088i", "Integrity": "SZ2mKaD4A+b+HIvttwl+TvLFnVy8o8/X40j+EKVwyvY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 203803, "LastWriteTime": "2025-06-21T20:31:49.6689666+00:00"}, "+eCRNcjV1KPRZZwo7LLzu3xCSlCqGLKSD/V24+zQ2Zw=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "8inm30yfxf", "Integrity": "gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 449111, "LastWriteTime": "2025-06-21T20:31:49.6674546+00:00"}, "0kFXo6GDThqcdFGIeMw5mUIqfzWQMnSaTsSWOSonVx0=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bpk8xqwxhs", "Integrity": "z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 162720, "LastWriteTime": "2025-06-21T20:31:49.6644575+00:00"}, "qVW4oeK9nEYTMdsyKXyU+O7AOCDNXWEe67lWD8QPDF8=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "73kdqttayv", "Integrity": "DRvWr0gangj5/5Q3DRn6ttzpcWDzl3OpHoAwAzNDR5Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 536547, "LastWriteTime": "2025-06-21T20:31:49.6624571+00:00"}, "97FZhD7eOZr6DvF0PPE/i1hvsrKVmSiQQc79SLkntk8=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mpyigms19s", "Integrity": "xlexqj9/k3uobVwGfciZcj/eDdooaNgcf4OFLtLUygM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 204136, "LastWriteTime": "2025-06-21T20:31:49.6254527+00:00"}, "fgMeb2BiKgQj7+ezxgh0fI8ahpves9+CfYj0o0dLdP4=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "56d2bn4wt9", "Integrity": "02ka4ymoE5yEecLUncLG3/SouTQMnTJOktX+96Pt/88=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 111710, "LastWriteTime": "2025-06-21T20:31:49.622449+00:00"}, "O028ka5sYH8PzwYMMgwlXsSQULdZHGgWa2McTp0I37Q=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "u3xrusw2ol", "Integrity": "Wi5ZuFSHLfx6dlEgjvW3BY9TC/1NqdBjj+XFifSSqN4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 53407, "LastWriteTime": "2025-06-21T20:31:49.6214487+00:00"}, "e23JlzphNt1xx/CguEYQZ+CkK4rLQgHmMWwm3LYzvF0=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ab1c3rmv7g", "Integrity": "puDgKwvlFAord9R8G8of9P2CVYIJUFSoIbjDLEsKEH0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 192214, "LastWriteTime": "2025-06-21T20:31:49.6204475+00:00"}, "4WrMXP0+bEIvqdqytvBpmNerGKnJTGqBhdMvzUfLXRs=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b4skse8du6", "Integrity": "peAGH8Gu/ZL9VnbUGSMN69Ji5MxwbvOb53gDXU2cPaQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 71451, "LastWriteTime": "2025-06-21T20:31:49.6184482+00:00"}, "pgXNFGRAZQlqRFg1pcHNnGOOLA56pxieYpF4ZmIWwFg=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vy0bq9ydhf", "Integrity": "p1dop4slefZhL4zG2pa6+2HUrOY1UUArGJXmet8Md9c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 111875, "LastWriteTime": "2025-06-21T20:31:49.617448+00:00"}, "E4XpVkYqesRkvcT/hpLjtTkq42M5nPGa9PZu1dt1eaA=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "16095smhkz", "Integrity": "5+ExmMkiaI3keYQRLhNibJ5ZXnNuWRbwrXOAZoTXMFg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 53479, "LastWriteTime": "2025-06-21T20:31:49.6164517+00:00"}, "RikoFhRwzzPwEoPlk6srdMa9MdVw1o3M+O3GS6T+g9Q=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "j75<PERSON><PERSON><PERSON>", "Integrity": "4WIqPof/vrXYO/jeJ4fDOQKUYWIwe64V3d+9/qNju20=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 192271, "LastWriteTime": "2025-06-21T20:31:49.615448+00:00"}, "uCGGhz7mRfYvdBRvFP8GMkKtMRp2QPnsPboc+OG2aIQ=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tey0rigmnh", "Integrity": "NbFZxZLmBVNLzb/7B0WdFfb6+8jXHGX6XY190uwgbec=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 71584, "LastWriteTime": "2025-06-21T20:31:49.6144477+00:00"}, "Obbz5ELi3L3kmMHAiJTJ6xwyKQtKLDmAA0TrYU7AWG0=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "wmug9u23qg", "Integrity": "GMDk5pA5dFkOimkBAWeEjYZ+7lgHPS0jYln6p/WJVYs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 41570, "LastWriteTime": "2025-06-21T20:31:49.6134473+00:00"}, "YuJI4llngV3rmg/ZxLblhrNvkZIQs9kdPQaOnTD0XCs=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "npxfuf8dg6", "Integrity": "a5KlgysZ4fQXw4rzIvXDHErFDPeHRSLccP7kX6HuvSQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 4718, "LastWriteTime": "2025-06-21T20:31:49.6124469+00:00"}, "oi9z1GVRu6vmju1xFo+yWvRT4BHK0DKKa0W9ShlRsks=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cwzlr5n8x4", "Integrity": "/EdWHN6t5XYPplC88vixGfrBvfEii19kAssb+0YBVU8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 105151, "LastWriteTime": "2025-06-21T20:31:49.6114511+00:00"}, "lkUdemlPb40SsTTdjF/873W6i1DmQ2aEOXmyDOrm7Zg=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0wve5yxp74", "Integrity": "8NXw3kF49FkQVPMdjnGDqoXXRU0TwzsLfCGbK9U8gnk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 5827, "LastWriteTime": "2025-06-21T20:31:49.6094632+00:00"}, "uBAZnAhwSQ8qaGCT8/oj2x4CbQSsLsgLvTOkgD0rDqo=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "okkk44j0xs", "Integrity": "2BbRsE/+czX1ufmDPGpnEieC9u6I3m5BKNDSX1ob3lg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 35330, "LastWriteTime": "2025-06-21T20:31:49.6094632+00:00"}, "cUt7xH6XvENHdShZHOcMgt90l1JtUgYQADbMEJCpWOU=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "f8imaxxbri", "Integrity": "z0OApR88UEocYXTXHU7Ueycaiib9XbDUmel9Gx0gbx4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 4646, "LastWriteTime": "2025-06-21T20:31:49.6044485+00:00"}, "ld8qIbkC0NlHPFLJODyToL02enBZPRayvKoX4KfYY3Q=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jeal3x0ldm", "Integrity": "FZG0KxbNqITUi4QY7QvPFRS/TccntMfFWfSTdHN/pws=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 105138, "LastWriteTime": "2025-06-21T20:31:49.6034478+00:00"}, "SHv3ScVw24grUWt4nefaFOgdyKWPUOENyf0xn9X9u/U=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "7na4sro3qu", "Integrity": "4zbWr0QNFhpUwGkn4WdGWXt80KnhRFv0qXkZyVnhajY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 5850, "LastWriteTime": "2025-06-21T20:31:49.6024535+00:00"}, "m+v2m0O/pO9UYkM6HNEDmmcc9x9TvG9F8qboHceKPJI=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "o371a8zbv2", "Integrity": "NDSZjIiMPRIoO7/w7+jHef8retP4riQa8PMj4BVRGok=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 117516, "LastWriteTime": "2025-06-21T20:31:49.6014491+00:00"}, "IHdHY5+9aFqNgryw1y0LXEZsq+VYwhdKpx2N+zKoO/Y=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "n1oizzvkh6", "Integrity": "O6lb2kXarGgVw4/RDD42yYPhZIwREthThQFKGmD+3j0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 51394, "LastWriteTime": "2025-06-21T20:31:49.6004486+00:00"}, "Lfryg9iOwXy1vkHkS6rVM6beBt2BielLDVk54G2fh9c=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2q4vfeazbq", "Integrity": "qvA39OMlEs53jaewqVFmE8DQQrio47bZtlTs+Wu6U8g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 196539, "LastWriteTime": "2025-06-21T20:31:49.5994483+00:00"}, "iX0ftjacqfbJB6uuce1JZcyEn6yKqfRAKMtlO/DEiG4=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "q2ku51ktnl", "Integrity": "3vUJkZSpKL/zG7x6GNvDjs0TxYUo9zMt6dAc8hp9CVo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 70612, "LastWriteTime": "2025-06-21T20:31:49.598448+00:00"}, "o6+KiWg9nye5dcn7jdn+t2rKCNUZlIJuZvNcX1YliuY=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5vj65cig9w", "Integrity": "72C/qDCGu+OwWeVA03bf9Ke0T8oIozCub0lfJkhzhvE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 117439, "LastWriteTime": "2025-06-21T20:31:49.5974486+00:00"}, "a1HFQ4mCM1iL9xyVKz102OJUzJQ3ZbqbN2CwAxJYMyo=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "unj9p35syc", "Integrity": "ysBT/JYxH9gcMnwxT4+MB4sPxOx/JMg9wi77FA13T9A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 51319, "LastWriteTime": "2025-06-21T20:31:49.5964491+00:00"}, "KYN2ylTO7LtUC7ax9OowUbOXy2GHFKiT9lZaHhRoY4M=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "st1cbwfwo5", "Integrity": "QO8cMbVkLiktUX1cHeXSUSe5nXMXUgyL9cjwnMyxPqc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 196535, "LastWriteTime": "2025-06-21T20:31:49.5954469+00:00"}, "E+AOJLIgM9S1plBPZR1QWfXziRVPJc/ibdlb+JlsOw8=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "agp80tu62r", "Integrity": "JtktgiuQAd+AXerCnPMrHCDz1h5AtkH5tobvpuG7xZ4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 70538, "LastWriteTime": "2025-06-21T20:31:49.5934482+00:00"}, "NrQJUImVTL5QzwT5dz3CJqEMrZrwfi9uvxM/+DlZ1cs=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\js\\supervisorOrders.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "js/supervisorOrders#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "f6elztjjd7", "Integrity": "B8JaBccOU5M1LUc55R9Hm5KdMawyQeLoQplGc1cL0do=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\supervisorOrders.js", "FileLength": 30643, "LastWriteTime": "2025-08-03T19:27:30.4477556+00:00"}, "UEBmQRFp99hDA4ayHKJ0ECxE5b7M6QwZbEp59JdblxY=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\js\\site.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "js/site#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "xtxxf3hu2r", "Integrity": "hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\site.js", "FileLength": 231, "LastWriteTime": "2025-06-21T20:31:49.4819116+00:00"}, "KShbojwqsuVuBXYoYtaNM5nYHlY7greUtdBz9w0W6Qo=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\js\\shared-utils.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "js/shared-utils#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lri8h1ahbj", "Integrity": "AUbExRbpgTQeJ2NcFAONm7tfP2qfaDzTipMD9/g9Wbk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\shared-utils.js", "FileLength": 8396, "LastWriteTime": "2025-08-03T21:25:17.5887463+00:00"}, "nmeP5xMMyhWplZMQClN6nh+ZyiceVkOIgPFzIs4oJAU=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\js\\pathManagement.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "js/pathManagement#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4auzfdj0yu", "Integrity": "DHdskasTgFAvd5mCSNulBaEaBDVHNbI7jJuMnB5WYrg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\pathManagement.js", "FileLength": 12313, "LastWriteTime": "2025-07-09T19:08:37.6821051+00:00"}, "JJ+T1xD3Jd0onDka0LcxD3XmpvJbPIYD9jL0dGCO5iE=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\js\\orderStatusNotifications.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "js/orderStatusNotifications#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "39xzyaekzs", "Integrity": "6cIReoeQvX2cnXy/VvWoUup3DdCJ3Q0Pl705IXyizm0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\orderStatusNotifications.js", "FileLength": 16164, "LastWriteTime": "2025-07-12T14:34:00.5576518+00:00"}, "HyGnE3/qi1/rcIDI3n2c9lGJHHdfH1NdcTS5OIgR4oM=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\js\\OrderDetals.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "js/OrderDetals#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "gzu5j5ybht", "Integrity": "5MwtiTZJSCPrz4Bmcvu9CDZCVGf+rPsiIihe1lk5Pyw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\OrderDetals.js", "FileLength": 11661, "LastWriteTime": "2025-07-15T19:41:24.3687229+00:00"}, "FaX5du/IS6uLUS/0dlSL8x1YdOe/B0aD7dCQy3YWln8=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\js\\orderDetailsModule.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "js/orderDetailsModule#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "081bj91i7y", "Integrity": "EN7SHh0AeWqJxzNSfxSkINS89PxC5omRGLOuhCh25xw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\orderDetailsModule.js", "FileLength": 12056, "LastWriteTime": "2025-08-03T19:47:20.9694571+00:00"}, "+gkzRJwQZdAlZhW1rr4h1VIcfYfK2OARiG/MjrogXVQ=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\js\\Notification.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "js/Notification#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cszcmsjyt3", "Integrity": "1EnteHesam+c0VBUafqQyR9M8NmV5MZA6Cf4ze/WbEo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\Notification.js", "FileLength": 4141, "LastWriteTime": "2025-05-12T00:24:40+00:00"}, "MMVCLYtjJJVRDKNHkI7B7oGUbw2TuNaaY6tWRYfvYb4=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\js\\hrmanager.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "js/hrmanager#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vdhy12noob", "Integrity": "g+WLA+RtP0NkCTc0IBmLwmviwp+zW/BhDZvXGwyMOUM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\hrmanager.js", "FileLength": 23488, "LastWriteTime": "2025-08-03T20:24:12.8151859+00:00"}, "OuGUo5LJbjnPoJqxlMjBhCzt0PgCFnm6FkxvJ+XLeic=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\js\\hrCoordinator.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "js/hrCoordinator#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "sb1df8xiqx", "Integrity": "+GauBI3Hb7kijzpdnW+9BSCxnglmSZBfZe5/iIpP/bw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\hrCoordinator.js", "FileLength": 31070, "LastWriteTime": "2025-08-03T19:27:30.5159747+00:00"}, "TBWPX4g1ibJZT6Ogt2GHS7i9ZTKRkti6QBeBMJcLaSc=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\js\\directManager.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "js/directManager#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bx8trjx6z8", "Integrity": "b4nMzWM3WzJh3BFWLm9B5X3fCm+8dHkNT/ZPFGAwC1c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\directManager.js", "FileLength": 7828, "LastWriteTime": "2025-08-02T15:52:43.0949209+00:00"}, "1eYTEtPR3dpd4Mlib1K0JAyG5UGD0ZTrDDjsHOtok2w=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\js\\dashboard.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "js/dashboard#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "sngigb5vex", "Integrity": "KK8M/xNJyMD/wtulTP5DfP2q5FT/KLR3naSk1/cX1y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\dashboard.js", "FileLength": 4031, "LastWriteTime": "2025-07-19T07:27:21.4071264+00:00"}, "lyypl0CLgXGxLYMPIAJ/BFrb+WsxRtqkII1PI1KGXZU=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\js\\assistantManager.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "js/assistant<PERSON>anager#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "l99oqmn03m", "Integrity": "F9n09QfxnCe/qljtvEf3bfWcy7RPdXAggAv1UKYWpa8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\assistantManager.js", "FileLength": 8478, "LastWriteTime": "2025-08-02T22:05:22.7011391+00:00"}, "SLtUNOmmU3fYUMuaJNfgCzQpTWi6ZQolcoQ2Le3s97A=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\js\\.eslintrc.json", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "js/.eslintrc#[.{fingerprint}]?.json", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "uu9sszpxz5", "Integrity": "5vi79dz2bVp7aGHRPP89A633hSv0mO36l6DWib2vysQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\.eslintrc.json", "FileLength": 1228, "LastWriteTime": "2025-05-03T13:16:51+00:00"}, "YVasURxL2jT67Xbcs4I+S6M0BDetPYkzydijgxdBC9c=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\css\\dashbord\\dashbord.css", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "css/dashbord/dashbord#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "hnh6ywcra9", "Integrity": "I//ribZr1OnQwjk3Eu45cbzm9HVUaMujTBN6dxeM5Xg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\dashbord\\dashbord.css", "FileLength": 35765, "LastWriteTime": "2025-08-01T16:24:56.4376596+00:00"}, "s5xl46AuqaOwwVQOlNxL7kcRODi0U+3rzR77YqS5T34=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\img\\star.png", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "img/star#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vbvgifvvkn", "Integrity": "ZV+qrTvBrNy2IVwDzfLhZalZ9U5VOCpF1ql7uEQAZzk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\img\\star.png", "FileLength": 152554, "LastWriteTime": "2025-05-14T17:40:25+00:00"}, "MVlg+z8WSC7ufBbJ5N6n8jcCOQrAiet3KTIrWNRpD9o=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\img\\Breada3.jpg", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "img/Breada3#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "3cvxl0parf", "Integrity": "HZ6PZANISBLVgsQdUMLQDL38HDroVuGIh2slbMNeG+k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\img\\Breada3.jpg", "FileLength": 114703, "LastWriteTime": "2025-05-14T15:55:54+00:00"}, "2Z2PwzjX2EtpAuZcv8fbzRI3dLkoThdevWeRfmnBgZY=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\img\\hero-svg-illustration.svg", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "img/hero-svg-illustration#[.{fingerprint}]?.svg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "rhdii9jgqu", "Integrity": "MaXR7x3vz5T5vVgpYi7392hhJIv/lh9PzodJNCTt2K4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\img\\hero-svg-illustration.svg", "FileLength": 6761, "LastWriteTime": "2025-06-27T14:04:00.5096616+00:00"}, "QNyp1AgPC2VxT/IPw4vFaoX6wKhkPtE02UdxJKkIxIQ=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\img\\Breada2.jpg", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "img/Breada2#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "f7qy3nga8b", "Integrity": "K9g9PlESiv0l4+2HEY5V+j/loHJxZYH6dH9BrcPOZp4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\img\\Breada2.jpg", "FileLength": 142247, "LastWriteTime": "2025-05-14T15:45:14+00:00"}, "jAMyC2LElYg+iwV6Fet6y/PdYniu5CF2fYRl5ONZyZk=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\img\\draw2.webp", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "img/draw2#[.{fingerprint}]?.webp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lb4spfgfnj", "Integrity": "4Rl+mf1UpaYn967Lz2UuVFzqBJdYMRAJAUtOw41/SGA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\img\\draw2.webp", "FileLength": 22372, "LastWriteTime": "2025-04-27T21:45:33+00:00"}, "tyc99F5214MJLopPUL75LoLvcgEn9x/tFi6LjB1vRs0=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\img\\Breada1.png", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "img/Breada1#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "r49qvtps7x", "Integrity": "u1NEt5uDu69/fXrFWrdVmBllySF4rOE3Ldb+p+eWiZk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\img\\Breada1.png", "FileLength": 126397, "LastWriteTime": "2025-06-27T13:58:30.4347512+00:00"}, "KfdivS3rIXzka8pqm91DdZxlDG62fOI2gebOT3mKm7I=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\img\\CardImage.jpg", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "img/CardImage#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "br1zpi7ud7", "Integrity": "pyR35oUyROVrLrOWWIOr/pS02JeYXmHud2TxL8b4w5g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\img\\CardImage.jpg", "FileLength": 13633, "LastWriteTime": "2025-05-13T20:35:24+00:00"}, "8tMsGoFHiEyTX6Hdhgubeq0FwItOh5uaiMwL7UGtxfA=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\favicon.ico", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "favicon#[.{fingerprint}]?.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "3djvn4e135", "Integrity": "OWWJaDDMsSyzwiAq56NrhqM61v7tYmOC+0cwr+SLErs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.ico", "FileLength": 4022, "LastWriteTime": "2025-06-27T14:07:54.218245+00:00"}, "DRgY2M224okKkAsZaSWg4qqz22u/ItLt2x1Lan3Gi0o=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\img\\Breada4.png", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "img/Breada4#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "avx9uya0f1", "Integrity": "SqYkqYyQHiqHWhROGoQY3riTbwLou2n3CWrxzS90dcE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\img\\Breada4.png", "FileLength": 319753, "LastWriteTime": "2025-05-14T15:58:29+00:00"}, "PXGczEHwRGhx4bQseVCb3q7MSPala6X0DwRptM6uRjc=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\css\\MainSite.css", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "css/MainSite#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cddbajxg2c", "Integrity": "cJ6pVLLZ8rMmm7taB027+uZ6xYW7HGPm3xxv24ijNYc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\MainSite.css", "FileLength": 39183, "LastWriteTime": "2025-07-19T13:10:51.0982377+00:00"}, "uWvKyI0eV2kDDhYnPXrNIY8QjjtOBPqTa59G1gSlEjc=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint}]?.md", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "x0q3zqp4vz", "Integrity": "geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 1117, "LastWriteTime": "2025-06-21T20:31:49.7885044+00:00"}, "fxXaZkDLEFvshO+ZtoCJTPQ4erMr6L3IZ068p7B1d7Q=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\jquery\\dist\\jquery.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fwhahm2icz", "Integrity": "H+K7U5CnXl1h5ywQfKtSj8PCmoN9aaq30gDh27Xc0jk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 288580, "LastWriteTime": "2025-06-21T20:31:49.4859141+00:00"}, "ZcHRLUcjrdw5neeu89GFoFjDtBqKTK0ptO5Iw3aFHBo=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\uploads\\order_1_bf69c15d-ff5e-41b2-bfb8-74eb832baa59_order_1_file1_20250627233043.pdf", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "uploads/order_1_bf69c15d-ff5e-41b2-bfb8-74eb832baa59_order_1_file1_20250627233043#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "wdbk1wabyj", "Integrity": "pPcNFOHBw4Ef3gPClk6tgsGHZT+tnzHeuazerOEO3wM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\order_1_bf69c15d-ff5e-41b2-bfb8-74eb832baa59_order_1_file1_20250627233043.pdf", "FileLength": 174317, "LastWriteTime": "2025-06-27T20:30:43.7510684+00:00"}, "lBQ/l1i8bertNKtETFhNWjeAcV7/tQpTvlYSjiHWqbY=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\js\\dashboard-charts.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "js/dashboard-charts#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2dw3qkz4nn", "Integrity": "liBNr2PGnkBsEQ2rztX5tUvoJp9HUg/bWdIaV5PALT8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\dashboard-charts.js", "FileLength": 9104, "LastWriteTime": "2025-07-05T10:48:11.9035287+00:00"}}, "CachedCopyCandidates": {}}